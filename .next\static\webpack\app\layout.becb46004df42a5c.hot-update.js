/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Ccomponents%5Cproviders.tsx&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-roboto%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Ccomponents%5Cproviders.tsx&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-roboto%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(app-pages-browser)/./components/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Roboto\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\",\"900\"],\"variable\":\"--font-roboto\",\"display\":\"swap\"}],\"variableName\":\"roboto\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Roboto\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-roboto\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"roboto\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Ccomponents%5Cproviders.tsx&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-roboto%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=false!\n"));

/***/ })

});