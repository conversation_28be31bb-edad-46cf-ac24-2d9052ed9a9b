// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  role          Role      @default(CUSTOMER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts Account[]
  sessions Session[]
  orders   Order[]
  reviews  Review[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  description String?
  image       String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  products Product[]
}

model Product {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String
  price       Decimal  @db.Decimal(10, 2)
  comparePrice Decimal? @db.Decimal(10, 2)
  images      String[]
  featured    Boolean  @default(false)
  inStock     Boolean  @default(true)
  inventory   Int      @default(0)
  sku         String?  @unique
  weight      Decimal? @db.Decimal(8, 2)
  dimensions  String?
  materials   String[]
  colors      String[]
  sizes       String[]
  tags        String[]
  seoTitle    String?
  seoDescription String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  categoryId String
  category   Category @relation(fields: [categoryId], references: [id])

  orderItems OrderItem[]
  reviews    Review[]
}

model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  status          OrderStatus @default(PENDING)
  total           Decimal     @db.Decimal(10, 2)
  subtotal        Decimal     @db.Decimal(10, 2)
  tax             Decimal     @db.Decimal(10, 2)
  shipping        Decimal     @db.Decimal(10, 2)
  discount        Decimal?    @db.Decimal(10, 2)
  paymentStatus   PaymentStatus @default(PENDING)
  paymentMethod   String?
  stripePaymentId String?
  
  // Shipping Information
  shippingName    String
  shippingEmail   String
  shippingPhone   String?
  shippingAddress String
  shippingCity    String
  shippingState   String
  shippingZip     String
  shippingCountry String @default("US")
  
  // Billing Information
  billingName     String
  billingEmail    String
  billingPhone    String?
  billingAddress  String
  billingCity     String
  billingState    String
  billingZip      String
  billingCountry  String @default("US")
  
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id])

  orderItems OrderItem[]
}

model OrderItem {
  id        String  @id @default(cuid())
  quantity  Int
  price     Decimal @db.Decimal(10, 2)
  size      String?
  color     String?

  orderId   String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)

  productId String
  product   Product @relation(fields: [productId], references: [id])

  @@unique([orderId, productId, size, color])
}

model Review {
  id        String   @id @default(cuid())
  rating    Int      @db.SmallInt
  title     String
  content   String
  verified  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId    String
  user      User    @relation(fields: [userId], references: [id])

  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
}

enum Role {
  CUSTOMER
  ADMIN
  SUPER_ADMIN
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}
