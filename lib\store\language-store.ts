import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { Language, getTranslation, Translations } from '@/lib/i18n'

interface LanguageStore {
  language: Language
  translations: Translations
  setLanguage: (language: Language) => void
}

export const useLanguageStore = create<LanguageStore>()(
  persist(
    (set) => ({
      language: 'en',
      translations: getTranslation('en'),
      setLanguage: (language: Language) => {
        set({
          language,
          translations: getTranslation(language),
        })
      },
    }),
    {
      name: 'language-storage',
    }
  )
)
