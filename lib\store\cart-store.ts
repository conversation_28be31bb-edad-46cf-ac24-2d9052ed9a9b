import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface CartItem {
  id: string
  name: string
  price: number
  image: string
  quantity: number
  size?: string
  color?: string
  sku?: string
}

interface CartStore {
  items: CartItem[]
  isOpen: boolean
  addItem: (item: Omit<CartItem, 'quantity'>) => void
  removeItem: (id: string, size?: string, color?: string) => void
  updateQuantity: (id: string, quantity: number, size?: string, color?: string) => void
  clearCart: () => void
  getTotalPrice: () => number
  getTotalItems: () => number
  setIsOpen: (isOpen: boolean) => void
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      isOpen: false,
      
      addItem: (newItem) => {
        const items = get().items
        const existingItemIndex = items.findIndex(
          item => 
            item.id === newItem.id && 
            item.size === newItem.size && 
            item.color === newItem.color
        )

        if (existingItemIndex > -1) {
          // Item exists, update quantity
          const updatedItems = [...items]
          updatedItems[existingItemIndex].quantity += 1
          set({ items: updatedItems })
        } else {
          // New item, add to cart
          set({ items: [...items, { ...newItem, quantity: 1 }] })
        }
      },

      removeItem: (id, size, color) => {
        const items = get().items.filter(
          item => !(
            item.id === id && 
            item.size === size && 
            item.color === color
          )
        )
        set({ items })
      },

      updateQuantity: (id, quantity, size, color) => {
        if (quantity <= 0) {
          get().removeItem(id, size, color)
          return
        }

        const items = get().items.map(item => {
          if (item.id === id && item.size === size && item.color === color) {
            return { ...item, quantity }
          }
          return item
        })
        set({ items })
      },

      clearCart: () => set({ items: [] }),

      getTotalPrice: () => {
        return get().items.reduce((total, item) => total + (item.price * item.quantity), 0)
      },

      getTotalItems: () => {
        return get().items.reduce((total, item) => total + item.quantity, 0)
      },

      setIsOpen: (isOpen) => set({ isOpen }),
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({ items: state.items }),
    }
  )
)
