"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTranslation: function() { return /* binding */ getTranslation; },\n/* harmony export */   translations: function() { return /* binding */ translations; }\n/* harmony export */ });\nconst translations = {\n    en: {\n        // Navigation\n        home: \"Home\",\n        shop: \"Shop\",\n        categories: \"Categories\",\n        about: \"About\",\n        contact: \"Contact\",\n        // Hero Section\n        heroTitle: \"Elite Professional Workwear\",\n        heroDescription: \"Premium workwear for professionals\",\n        shopNow: \"Shop Now\",\n        learnMore: \"Learn More\",\n        // Products\n        featuredProducts: \"Elite Collection\",\n        featuredDescription: \"Handcrafted premium workwear for the most discerning professionals\",\n        addToCart: \"Add to Cart\",\n        viewDetails: \"View Details\",\n        price: \"Price\",\n        // Categories\n        categoriesTitle: \"Premium Categories\",\n        categoriesDescription: \"Explore our exclusive collections designed for excellence\",\n        officeWear: \"Executive Office Wear\",\n        industrialWear: \"Premium Industrial Gear\",\n        healthcareUniforms: \"Luxury Medical Uniforms\",\n        // Footer\n        followUs: \"Follow Us\",\n        allRightsReserved: \"All rights reserved.\",\n        // Admin\n        dashboard: \"Dashboard\",\n        products: \"Products\",\n        orders: \"Orders\",\n        customers: \"Customers\",\n        settings: \"Settings\",\n        addProduct: \"Add Product\",\n        // Common\n        loading: \"Loading...\",\n        error: \"Error\",\n        success: \"Success\",\n        save: \"Save\",\n        cancel: \"Cancel\",\n        delete: \"Delete\",\n        edit: \"Edit\"\n    },\n    tr: {\n        // Navigation\n        home: \"Ana Sayfa\",\n        shop: \"Mağaza\",\n        categories: \"Kategoriler\",\n        about: \"Hakkımızda\",\n        contact: \"İletişim\",\n        // Hero Section\n        heroTitle: \"Elite Profesyonel İş Kıyafetleri\",\n        heroSubtitle: \"L\\xfcks • Premium • M\\xfckemmellik\",\n        heroDescription: \"End\\xfcstri liderlerine \\xf6zel tasarlanmış ultra-premium iş kıyafetleri koleksiyonumuzla profesyonel giyimin zirvesini deneyimleyin.\",\n        shopNow: \"Elite Koleksiyonu Keşfet\",\n        learnMore: \"M\\xfckemmelliği Keşfet\",\n        // Products\n        featuredProducts: \"Elite Koleksiyon\",\n        featuredDescription: \"En se\\xe7kin profesyoneller i\\xe7in el yapımı premium iş kıyafetleri\",\n        addToCart: \"Sepete Ekle\",\n        viewDetails: \"Detayları G\\xf6r\",\n        price: \"Fiyat\",\n        // Categories\n        categoriesTitle: \"Premium Kategoriler\",\n        categoriesDescription: \"M\\xfckemmellik i\\xe7in tasarlanmış \\xf6zel koleksiyonlarımızı keşfedin\",\n        officeWear: \"Y\\xf6netici Ofis Kıyafetleri\",\n        industrialWear: \"Premium End\\xfcstriyel Ekipman\",\n        healthcareUniforms: \"L\\xfcks Tıbbi \\xdcniformalar\",\n        // Footer\n        followUs: \"Bizi Takip Edin\",\n        allRightsReserved: \"T\\xfcm hakları saklıdır.\",\n        // Admin\n        dashboard: \"Kontrol Paneli\",\n        products: \"\\xdcr\\xfcnler\",\n        orders: \"Siparişler\",\n        customers: \"M\\xfcşteriler\",\n        settings: \"Ayarlar\",\n        addProduct: \"\\xdcr\\xfcn Ekle\",\n        // Common\n        loading: \"Y\\xfckleniyor...\",\n        error: \"Hata\",\n        success: \"Başarılı\",\n        save: \"Kaydet\",\n        cancel: \"İptal\",\n        delete: \"Sil\",\n        edit: \"D\\xfczenle\"\n    },\n    de: {\n        // Navigation\n        home: \"Startseite\",\n        shop: \"Shop\",\n        categories: \"Kategorien\",\n        about: \"\\xdcber uns\",\n        contact: \"Kontakt\",\n        // Hero Section\n        heroTitle: \"Elite Professionelle Arbeitskleidung\",\n        heroSubtitle: \"Luxus • Premium • Exzellenz\",\n        heroDescription: \"Erleben Sie den Gipfel professioneller Kleidung mit unserer exklusiven Kollektion ultra-premium Arbeitskleidung f\\xfcr Branchenf\\xfchrer.\",\n        shopNow: \"Elite Kollektion Shoppen\",\n        learnMore: \"Exzellenz Entdecken\",\n        // Products\n        featuredProducts: \"Elite Kollektion\",\n        featuredDescription: \"Handgefertigte Premium-Arbeitskleidung f\\xfcr anspruchsvollste Profis\",\n        addToCart: \"In den Warenkorb\",\n        viewDetails: \"Details Anzeigen\",\n        price: \"Preis\",\n        // Categories\n        categoriesTitle: \"Premium Kategorien\",\n        categoriesDescription: \"Entdecken Sie unsere exklusiven Kollektionen f\\xfcr Exzellenz\",\n        officeWear: \"Executive B\\xfcrokleidung\",\n        industrialWear: \"Premium Industrieausr\\xfcstung\",\n        healthcareUniforms: \"Luxus Medizinische Uniformen\",\n        // Footer\n        followUs: \"Folgen Sie uns\",\n        allRightsReserved: \"Alle Rechte vorbehalten.\",\n        // Admin\n        dashboard: \"Dashboard\",\n        products: \"Produkte\",\n        orders: \"Bestellungen\",\n        customers: \"Kunden\",\n        settings: \"Einstellungen\",\n        addProduct: \"Produkt Hinzuf\\xfcgen\",\n        // Common\n        loading: \"Laden...\",\n        error: \"Fehler\",\n        success: \"Erfolg\",\n        save: \"Speichern\",\n        cancel: \"Abbrechen\",\n        delete: \"L\\xf6schen\",\n        edit: \"Bearbeiten\"\n    }\n};\nfunction getTranslation(language) {\n    return translations[language] || translations.en;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n.ts\n"));

/***/ })

});