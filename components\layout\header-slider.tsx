'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useLanguageStore } from '@/lib/store/language-store'

const slides = [
  {
    id: 1,
    title: {
      en: '🏆 ELITE COLLECTION',
      tr: '🏆 ELİTE KOLEKSİYON',
      de: '🏆 ELITE KOLLEKTION'
    },
    subtitle: {
      en: 'Premium Workwear for Industry Leaders',
      tr: 'Endüstri Liderleri için Premium İş Kıyafetleri',
      de: 'Premium Arbeitskleidung für Branchenführer'
    },
    gradient: 'from-luxury-500 via-premium-500 to-vibrant-500'
  },
  {
    id: 2,
    title: {
      en: '💎 LUXURY REDEFINED',
      tr: '💎 LÜKS YENİDEN TANIMLANDI',
      de: '💎 LUXUS NEU DEFINIERT'
    },
    subtitle: {
      en: 'Exceptional Quality • Unmatched Style',
      tr: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> • Eşsiz Stil',
      de: 'Außergewöhnliche Qualität • Unvergleichlicher Stil'
    },
    gradient: 'from-premium-600 via-vibrant-500 to-luxury-500'
  },
  {
    id: 3,
    title: {
      en: '⚡ FAST & PREMIUM',
      tr: '⚡ HIZLI & PREMIUM',
      de: '⚡ SCHNELL & PREMIUM'
    },
    subtitle: {
      en: 'Lightning Fast Delivery • World-Class Service',
      tr: 'Şimşek Hızında Teslimat • Dünya Standartında Hizmet',
      de: 'Blitzschnelle Lieferung • Weltklasse-Service'
    },
    gradient: 'from-vibrant-500 via-luxury-500 to-premium-500'
  }
]

export function HeaderSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const { language } = useLanguageStore()

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 4000)

    return () => clearInterval(timer)
  }, [])

  return (
    <div className="relative h-16 overflow-hidden bg-gradient-to-r from-elite-900 to-elite-800 border-b-2 border-luxury-400">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ x: 300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: -300, opacity: 0 }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
          className={`absolute inset-0 bg-gradient-to-r ${slides[currentSlide].gradient} flex items-center justify-center`}
        >
          <div className="text-center">
            <motion.h3
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-white font-black text-lg md:text-xl font-display tracking-wider animate-glow"
            >
              {slides[currentSlide].title[language]}
            </motion.h3>
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="text-white/90 text-sm md:text-base font-medium mt-1"
            >
              {slides[currentSlide].subtitle[language]}
            </motion.p>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Slide Indicators */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === currentSlide 
                ? 'bg-white shadow-lg scale-125' 
                : 'bg-white/50 hover:bg-white/75'
            }`}
          />
        ))}
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{ 
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360]
          }}
          transition={{ 
            duration: 20, 
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="absolute -top-4 -right-4 w-8 h-8 bg-white/10 rounded-full blur-sm"
        />
        <motion.div
          animate={{ 
            x: [0, -80, 0],
            y: [0, 30, 0],
            rotate: [360, 180, 0]
          }}
          transition={{ 
            duration: 15, 
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="absolute -bottom-2 -left-2 w-6 h-6 bg-white/10 rounded-full blur-sm"
        />
      </div>
    </div>
  )
}
