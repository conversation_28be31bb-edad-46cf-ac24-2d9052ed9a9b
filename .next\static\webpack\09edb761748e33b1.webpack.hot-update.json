{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Ccomponents%5Cproviders.tsx&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Roboto%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22700%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-roboto%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22roboto%22%7D&modules=C%3A%5CUsers%5CBen%5COneDrive%5CDesktop%5CNewShop%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=false!"]}