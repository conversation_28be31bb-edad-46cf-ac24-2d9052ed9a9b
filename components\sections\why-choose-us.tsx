'use client'

import { motion } from 'framer-motion'
import { 
  ShieldCheckIcon, 
  TruckIcon, 
  StarIcon, 
  HeartIcon 
} from '@heroicons/react/24/outline'

const features = [
  {
    icon: ShieldCheckIcon,
    title: 'Premium Quality',
    description: 'Every piece is crafted with the finest materials and attention to detail'
  },
  {
    icon: TruckIcon,
    title: 'Fast Delivery',
    description: 'Express shipping worldwide with tracking and insurance included'
  },
  {
    icon: StarIcon,
    title: 'Expert Tailoring',
    description: 'Professional alterations and custom fitting services available'
  },
  {
    icon: HeartIcon,
    title: 'Customer Care',
    description: '24/7 support and satisfaction guarantee on all purchases'
  }
]

export function WhyChooseUs() {
  return (
    <section className="py-20 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Why Choose Elite Workwear
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Experience the difference that premium quality and exceptional service make
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 mx-auto mb-6 work-gradient rounded-full flex items-center justify-center">
                <feature.icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">
                {feature.title}
              </h3>
              <p className="text-gray-300">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
