# Elite Workwear - Premium Work Clothing E-commerce Platform

A luxury e-commerce platform for premium workwear and professional clothing, built with Next.js 14, TypeScript, and modern web technologies.

## 🌟 Features

### Customer-Facing Features
- **Premium Design**: Luxury aesthetic with smooth animations and premium feel
- **Product Catalog**: Advanced filtering, search, and product browsing
- **Shopping Cart**: Persistent cart with size/color variants
- **User Authentication**: Secure login/registration with NextAuth.js
- **Responsive Design**: Mobile-first approach with beautiful UI
- **SEO Optimized**: Server-side rendering and meta optimization
- **Performance**: Optimized images, lazy loading, and fast page loads

### Admin Panel Features
- **Dashboard**: Sales analytics, order management, and key metrics
- **Product Management**: Full CRUD operations for products
- **Order Management**: Track and manage customer orders
- **Customer Management**: View and manage customer accounts
- **Inventory Tracking**: Real-time stock management
- **Analytics**: Sales reports and performance insights

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom luxury theme
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **State Management**: Zustand
- **Animations**: Framer Motion
- **UI Components**: Radix UI + Custom components
- **Image Handling**: Next.js Image optimization + Cloudinary
- **Payments**: Stripe integration
- **Email**: SMTP integration for order confirmations

## 🚀 Getting Started

### Prerequisites

Before you begin, ensure you have the following installed:
- Node.js 18+ 
- npm or yarn
- PostgreSQL database
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd NewShop
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Fill in your environment variables in `.env.local`:
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/workwear_shop"
   
   # NextAuth
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key-here"
   
   # Google OAuth (optional)
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"
   
   # Stripe
   STRIPE_PUBLISHABLE_KEY="pk_test_..."
   STRIPE_SECRET_KEY="sk_test_..."
   
   # Cloudinary
   CLOUDINARY_CLOUD_NAME="your-cloud-name"
   CLOUDINARY_API_KEY="your-api-key"
   CLOUDINARY_API_SECRET="your-api-secret"
   ```

4. **Set up the database**
   ```bash
   # Generate Prisma client
   npx prisma generate
   
   # Push database schema
   npx prisma db push
   
   # (Optional) Seed the database
   npm run db:seed
   ```

5. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
├── app/                    # Next.js 14 app directory
│   ├── admin/             # Admin panel pages
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Homepage
├── components/            # Reusable components
│   ├── admin/             # Admin-specific components
│   ├── cart/              # Shopping cart components
│   ├── layout/            # Layout components (header, footer)
│   ├── modals/            # Modal components
│   ├── sections/          # Page sections
│   └── ui/                # Base UI components
├── lib/                   # Utilities and configurations
│   ├── store/             # Zustand stores
│   ├── auth.ts            # NextAuth configuration
│   ├── db.ts              # Database connection
│   └── utils.ts           # Utility functions
├── prisma/                # Database schema and migrations
│   └── schema.prisma      # Prisma schema
├── public/                # Static assets
└── types/                 # TypeScript type definitions
```

## 🎨 Design System

The project uses a custom luxury design system with:
- **Colors**: Luxury gold palette with professional grays
- **Typography**: Inter (sans-serif) + Playfair Display (serif)
- **Components**: Premium styled components with hover effects
- **Animations**: Smooth transitions and micro-interactions

## 🔐 Authentication

The platform supports multiple authentication methods:
- Email/Password authentication
- Google OAuth (configurable)
- Admin role-based access control

## 💳 Payment Integration

Stripe integration for secure payments:
- Credit card processing
- Subscription support (if needed)
- Webhook handling for order updates

## 📱 Admin Panel

Access the admin panel at `/admin` with admin credentials:
- Dashboard with key metrics
- Product management (CRUD operations)
- Order tracking and management
- Customer management
- Analytics and reporting

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables
4. Deploy automatically

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🧪 Testing

```bash
# Run tests (when implemented)
npm run test

# Run linting
npm run lint

# Type checking
npm run type-check
```

## 📈 Performance

The platform is optimized for performance:
- Server-side rendering (SSR)
- Image optimization
- Code splitting
- Lazy loading
- Caching strategies

## 🔧 Customization

### Adding New Products
1. Use the admin panel at `/admin/products`
2. Or add directly via Prisma Studio: `npx prisma studio`

### Styling
- Modify `tailwind.config.ts` for theme changes
- Update `app/globals.css` for global styles
- Component styles in individual component files

### Database Schema
- Modify `prisma/schema.prisma`
- Run `npx prisma db push` to apply changes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the code comments

## 🎯 Roadmap

- [ ] Advanced search and filtering
- [ ] Wishlist functionality
- [ ] Product reviews and ratings
- [ ] Multi-language support
- [ ] Advanced analytics
- [ ] Mobile app (React Native)
- [ ] AI-powered recommendations

---

**Elite Workwear** - Where luxury meets functionality in professional attire.
