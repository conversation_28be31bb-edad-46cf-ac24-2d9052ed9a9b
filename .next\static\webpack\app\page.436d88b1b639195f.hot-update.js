"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTranslation: function() { return /* binding */ getTranslation; },\n/* harmony export */   translations: function() { return /* binding */ translations; }\n/* harmony export */ });\nconst translations = {\n    en: {\n        // Navigation\n        home: \"Home\",\n        shop: \"Shop\",\n        categories: \"Categories\",\n        about: \"About\",\n        contact: \"Contact\",\n        // Hero Section\n        heroTitle: \"Elite Professional Workwear\",\n        heroDescription: \"Premium workwear for professionals\",\n        shopNow: \"Shop Now\",\n        learnMore: \"Learn More\",\n        // Products\n        featuredProducts: \"Elite Collection\",\n        featuredDescription: \"Handcrafted premium workwear for the most discerning professionals\",\n        addToCart: \"Add to Cart\",\n        viewDetails: \"View Details\",\n        price: \"Price\",\n        // Categories\n        categoriesTitle: \"Premium Categories\",\n        categoriesDescription: \"Explore our exclusive collections designed for excellence\",\n        officeWear: \"Executive Office Wear\",\n        industrialWear: \"Premium Industrial Gear\",\n        healthcareUniforms: \"Luxury Medical Uniforms\",\n        // Footer\n        followUs: \"Follow Us\",\n        allRightsReserved: \"All rights reserved.\",\n        // Admin\n        dashboard: \"Dashboard\",\n        products: \"Products\",\n        orders: \"Orders\",\n        customers: \"Customers\",\n        settings: \"Settings\",\n        addProduct: \"Add Product\",\n        // Common\n        loading: \"Loading...\",\n        error: \"Error\",\n        success: \"Success\",\n        save: \"Save\",\n        cancel: \"Cancel\",\n        delete: \"Delete\",\n        edit: \"Edit\"\n    },\n    tr: {\n        // Navigation\n        home: \"Ana Sayfa\",\n        shop: \"Mağaza\",\n        categories: \"Kategoriler\",\n        about: \"Hakkımızda\",\n        contact: \"İletişim\",\n        // Hero Section\n        heroTitle: \"Elite Profesyonel İş Kıyafetleri\",\n        heroDescription: \"Profesyoneller i\\xe7in premium iş kıyafetleri\",\n        shopNow: \"Alışveriş Yap\",\n        learnMore: \"Daha Fazla\",\n        // Products\n        featuredProducts: \"Elite Koleksiyon\",\n        featuredDescription: \"En se\\xe7kin profesyoneller i\\xe7in el yapımı premium iş kıyafetleri\",\n        addToCart: \"Sepete Ekle\",\n        viewDetails: \"Detayları G\\xf6r\",\n        price: \"Fiyat\",\n        // Categories\n        categoriesTitle: \"Premium Kategoriler\",\n        categoriesDescription: \"M\\xfckemmellik i\\xe7in tasarlanmış \\xf6zel koleksiyonlarımızı keşfedin\",\n        officeWear: \"Y\\xf6netici Ofis Kıyafetleri\",\n        industrialWear: \"Premium End\\xfcstriyel Ekipman\",\n        healthcareUniforms: \"L\\xfcks Tıbbi \\xdcniformalar\",\n        // Footer\n        followUs: \"Bizi Takip Edin\",\n        allRightsReserved: \"T\\xfcm hakları saklıdır.\",\n        // Admin\n        dashboard: \"Kontrol Paneli\",\n        products: \"\\xdcr\\xfcnler\",\n        orders: \"Siparişler\",\n        customers: \"M\\xfcşteriler\",\n        settings: \"Ayarlar\",\n        addProduct: \"\\xdcr\\xfcn Ekle\",\n        // Common\n        loading: \"Y\\xfckleniyor...\",\n        error: \"Hata\",\n        success: \"Başarılı\",\n        save: \"Kaydet\",\n        cancel: \"İptal\",\n        delete: \"Sil\",\n        edit: \"D\\xfczenle\"\n    },\n    de: {\n        // Navigation\n        home: \"Startseite\",\n        shop: \"Shop\",\n        categories: \"Kategorien\",\n        about: \"\\xdcber uns\",\n        contact: \"Kontakt\",\n        // Hero Section\n        heroTitle: \"Elite Professionelle Arbeitskleidung\",\n        heroDescription: \"Premium Arbeitskleidung f\\xfcr Profis\",\n        shopNow: \"Jetzt Einkaufen\",\n        learnMore: \"Mehr Erfahren\",\n        // Products\n        featuredProducts: \"Elite Kollektion\",\n        featuredDescription: \"Handgefertigte Premium-Arbeitskleidung f\\xfcr anspruchsvollste Profis\",\n        addToCart: \"In den Warenkorb\",\n        viewDetails: \"Details Anzeigen\",\n        price: \"Preis\",\n        // Categories\n        categoriesTitle: \"Premium Kategorien\",\n        categoriesDescription: \"Entdecken Sie unsere exklusiven Kollektionen f\\xfcr Exzellenz\",\n        officeWear: \"Executive B\\xfcrokleidung\",\n        industrialWear: \"Premium Industrieausr\\xfcstung\",\n        healthcareUniforms: \"Luxus Medizinische Uniformen\",\n        // Footer\n        followUs: \"Folgen Sie uns\",\n        allRightsReserved: \"Alle Rechte vorbehalten.\",\n        // Admin\n        dashboard: \"Dashboard\",\n        products: \"Produkte\",\n        orders: \"Bestellungen\",\n        customers: \"Kunden\",\n        settings: \"Einstellungen\",\n        addProduct: \"Produkt Hinzuf\\xfcgen\",\n        // Common\n        loading: \"Laden...\",\n        error: \"Fehler\",\n        success: \"Erfolg\",\n        save: \"Speichern\",\n        cancel: \"Abbrechen\",\n        delete: \"L\\xf6schen\",\n        edit: \"Bearbeiten\"\n    }\n};\nfunction getTranslation(language) {\n    return translations[language] || translations.en;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n.ts\n"));

/***/ })

});