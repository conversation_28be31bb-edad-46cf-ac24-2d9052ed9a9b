"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    #focused;\n    #cleanup;\n    #setup;\n    constructor(){\n        super();\n        this.#setup = (onFocus)=>{\n            if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n                const listener = ()=>onFocus();\n                window.addEventListener(\"visibilitychange\", listener, false);\n                return ()=>{\n                    window.removeEventListener(\"visibilitychange\", listener);\n                };\n            }\n            return;\n        };\n    }\n    onSubscribe() {\n        if (!this.#cleanup) {\n            this.setEventListener(this.#setup);\n        }\n    }\n    onUnsubscribe() {\n        if (!this.hasListeners()) {\n            this.#cleanup?.();\n            this.#cleanup = void 0;\n        }\n    }\n    setEventListener(setup) {\n        this.#setup = setup;\n        this.#cleanup?.();\n        this.#cleanup = setup((focused)=>{\n            if (typeof focused === \"boolean\") {\n                this.setFocused(focused);\n            } else {\n                this.onFocus();\n            }\n        });\n    }\n    setFocused(focused) {\n        const changed = this.#focused !== focused;\n        if (changed) {\n            this.#focused = focused;\n            this.onFocus();\n        }\n    }\n    onFocus() {\n        const isFocused = this.isFocused();\n        this.listeners.forEach((listener)=>{\n            listener(isFocused);\n        });\n    }\n    isFocused() {\n        if (typeof this.#focused === \"boolean\") {\n            return this.#focused;\n        }\n        return globalThis.document?.visibilityState !== \"hidden\";\n    }\n};\nvar focusManager = new FocusManager();\n //# sourceMappingURL=focusManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/infiniteQueryBehavior.ts\n\nfunction infiniteQueryBehavior(pages) {\n    return {\n        onFetch: (context, query)=>{\n            const options = context.options;\n            const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n            const oldPages = context.state.data?.pages || [];\n            const oldPageParams = context.state.data?.pageParams || [];\n            let result = {\n                pages: [],\n                pageParams: []\n            };\n            let currentPage = 0;\n            const fetchFn = async ()=>{\n                let cancelled = false;\n                const addSignalProperty = (object)=>{\n                    Object.defineProperty(object, \"signal\", {\n                        enumerable: true,\n                        get: ()=>{\n                            if (context.signal.aborted) {\n                                cancelled = true;\n                            } else {\n                                context.signal.addEventListener(\"abort\", ()=>{\n                                    cancelled = true;\n                                });\n                            }\n                            return context.signal;\n                        }\n                    });\n                };\n                const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureQueryFn)(context.options, context.fetchOptions);\n                const fetchPage = async (data, param, previous)=>{\n                    if (cancelled) {\n                        return Promise.reject();\n                    }\n                    if (param == null && data.pages.length) {\n                        return Promise.resolve(data);\n                    }\n                    const createQueryFnContext = ()=>{\n                        const queryFnContext2 = {\n                            client: context.client,\n                            queryKey: context.queryKey,\n                            pageParam: param,\n                            direction: previous ? \"backward\" : \"forward\",\n                            meta: context.options.meta\n                        };\n                        addSignalProperty(queryFnContext2);\n                        return queryFnContext2;\n                    };\n                    const queryFnContext = createQueryFnContext();\n                    const page = await queryFn(queryFnContext);\n                    const { maxPages } = context.options;\n                    const addTo = previous ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToStart : _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToEnd;\n                    return {\n                        pages: addTo(data.pages, page, maxPages),\n                        pageParams: addTo(data.pageParams, param, maxPages)\n                    };\n                };\n                if (direction && oldPages.length) {\n                    const previous = direction === \"backward\";\n                    const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n                    const oldData = {\n                        pages: oldPages,\n                        pageParams: oldPageParams\n                    };\n                    const param = pageParamFn(options, oldData);\n                    result = await fetchPage(oldData, param, previous);\n                } else {\n                    const remainingPages = pages ?? oldPages.length;\n                    do {\n                        const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n                        if (currentPage > 0 && param == null) {\n                            break;\n                        }\n                        result = await fetchPage(result, param);\n                        currentPage++;\n                    }while (currentPage < remainingPages);\n                }\n                return result;\n            };\n            if (context.options.persister) {\n                context.fetchFn = ()=>{\n                    return context.options.persister?.(fetchFn, {\n                        client: context.client,\n                        queryKey: context.queryKey,\n                        meta: context.options.meta,\n                        signal: context.signal\n                    }, query);\n                };\n            } else {\n                context.fetchFn = fetchFn;\n            }\n        }\n    };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n    const lastIndex = pages.length - 1;\n    return pages.length > 0 ? options.getNextPageParam(pages[lastIndex], pages, pageParams[lastIndex], pageParams) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n    return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n    if (!data) return false;\n    return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n    if (!data || !options.getPreviousPageParam) return false;\n    return getPreviousPageParam(options, data) != null;\n}\n //# sourceMappingURL=infiniteQueryBehavior.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n    #observers;\n    #mutationCache;\n    #retryer;\n    constructor(config){\n        super();\n        this.mutationId = config.mutationId;\n        this.#mutationCache = config.mutationCache;\n        this.#observers = [];\n        this.state = config.state || getDefaultState();\n        this.setOptions(config.options);\n        this.scheduleGc();\n    }\n    setOptions(options) {\n        this.options = options;\n        this.updateGcTime(this.options.gcTime);\n    }\n    get meta() {\n        return this.options.meta;\n    }\n    addObserver(observer) {\n        if (!this.#observers.includes(observer)) {\n            this.#observers.push(observer);\n            this.clearGcTimeout();\n            this.#mutationCache.notify({\n                type: \"observerAdded\",\n                mutation: this,\n                observer\n            });\n        }\n    }\n    removeObserver(observer) {\n        this.#observers = this.#observers.filter((x)=>x !== observer);\n        this.scheduleGc();\n        this.#mutationCache.notify({\n            type: \"observerRemoved\",\n            mutation: this,\n            observer\n        });\n    }\n    optionalRemove() {\n        if (!this.#observers.length) {\n            if (this.state.status === \"pending\") {\n                this.scheduleGc();\n            } else {\n                this.#mutationCache.remove(this);\n            }\n        }\n    }\n    continue() {\n        return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n        this.execute(this.state.variables);\n    }\n    async execute(variables) {\n        const onContinue = ()=>{\n            this.#dispatch({\n                type: \"continue\"\n            });\n        };\n        this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n            fn: ()=>{\n                if (!this.options.mutationFn) {\n                    return Promise.reject(new Error(\"No mutationFn found\"));\n                }\n                return this.options.mutationFn(variables);\n            },\n            onFail: (failureCount, error)=>{\n                this.#dispatch({\n                    type: \"failed\",\n                    failureCount,\n                    error\n                });\n            },\n            onPause: ()=>{\n                this.#dispatch({\n                    type: \"pause\"\n                });\n            },\n            onContinue,\n            retry: this.options.retry ?? 0,\n            retryDelay: this.options.retryDelay,\n            networkMode: this.options.networkMode,\n            canRun: ()=>this.#mutationCache.canRun(this)\n        });\n        const restored = this.state.status === \"pending\";\n        const isPaused = !this.#retryer.canStart();\n        try {\n            if (restored) {\n                onContinue();\n            } else {\n                this.#dispatch({\n                    type: \"pending\",\n                    variables,\n                    isPaused\n                });\n                await this.#mutationCache.config.onMutate?.(variables, this);\n                const context = await this.options.onMutate?.(variables);\n                if (context !== this.state.context) {\n                    this.#dispatch({\n                        type: \"pending\",\n                        context,\n                        variables,\n                        isPaused\n                    });\n                }\n            }\n            const data = await this.#retryer.start();\n            await this.#mutationCache.config.onSuccess?.(data, variables, this.state.context, this);\n            await this.options.onSuccess?.(data, variables, this.state.context);\n            await this.#mutationCache.config.onSettled?.(data, null, this.state.variables, this.state.context, this);\n            await this.options.onSettled?.(data, null, variables, this.state.context);\n            this.#dispatch({\n                type: \"success\",\n                data\n            });\n            return data;\n        } catch (error) {\n            try {\n                await this.#mutationCache.config.onError?.(error, variables, this.state.context, this);\n                await this.options.onError?.(error, variables, this.state.context);\n                await this.#mutationCache.config.onSettled?.(void 0, error, this.state.variables, this.state.context, this);\n                await this.options.onSettled?.(void 0, error, variables, this.state.context);\n                throw error;\n            } finally{\n                this.#dispatch({\n                    type: \"error\",\n                    error\n                });\n            }\n        } finally{\n            this.#mutationCache.runNext(this);\n        }\n    }\n    #dispatch(action) {\n        const reducer = (state)=>{\n            switch(action.type){\n                case \"failed\":\n                    return {\n                        ...state,\n                        failureCount: action.failureCount,\n                        failureReason: action.error\n                    };\n                case \"pause\":\n                    return {\n                        ...state,\n                        isPaused: true\n                    };\n                case \"continue\":\n                    return {\n                        ...state,\n                        isPaused: false\n                    };\n                case \"pending\":\n                    return {\n                        ...state,\n                        context: action.context,\n                        data: void 0,\n                        failureCount: 0,\n                        failureReason: null,\n                        error: null,\n                        isPaused: action.isPaused,\n                        status: \"pending\",\n                        variables: action.variables,\n                        submittedAt: Date.now()\n                    };\n                case \"success\":\n                    return {\n                        ...state,\n                        data: action.data,\n                        failureCount: 0,\n                        failureReason: null,\n                        error: null,\n                        status: \"success\",\n                        isPaused: false\n                    };\n                case \"error\":\n                    return {\n                        ...state,\n                        data: void 0,\n                        error: action.error,\n                        failureCount: state.failureCount + 1,\n                        failureReason: action.error,\n                        isPaused: false,\n                        status: \"error\"\n                    };\n            }\n        };\n        this.state = reducer(this.state);\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.#observers.forEach((observer)=>{\n                observer.onMutationUpdate(action);\n            });\n            this.#mutationCache.notify({\n                mutation: this,\n                type: \"updated\",\n                action\n            });\n        });\n    }\n};\nfunction getDefaultState() {\n    return {\n        context: void 0,\n        data: void 0,\n        error: null,\n        failureCount: 0,\n        failureReason: null,\n        isPaused: false,\n        status: \"idle\",\n        variables: void 0,\n        submittedAt: 0\n    };\n}\n //# sourceMappingURL=mutation.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationCache.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/mutationCache.ts\n\n\n\n\nvar MutationCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    constructor(config = {}){\n        super();\n        this.config = config;\n        this.#mutations = /* @__PURE__ */ new Set();\n        this.#scopes = /* @__PURE__ */ new Map();\n        this.#mutationId = 0;\n    }\n    #mutations;\n    #scopes;\n    #mutationId;\n    build(client, options, state) {\n        const mutation = new _mutation_js__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n            mutationCache: this,\n            mutationId: ++this.#mutationId,\n            options: client.defaultMutationOptions(options),\n            state\n        });\n        this.add(mutation);\n        return mutation;\n    }\n    add(mutation) {\n        this.#mutations.add(mutation);\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const scopedMutations = this.#scopes.get(scope);\n            if (scopedMutations) {\n                scopedMutations.push(mutation);\n            } else {\n                this.#scopes.set(scope, [\n                    mutation\n                ]);\n            }\n        }\n        this.notify({\n            type: \"added\",\n            mutation\n        });\n    }\n    remove(mutation) {\n        if (this.#mutations.delete(mutation)) {\n            const scope = scopeFor(mutation);\n            if (typeof scope === \"string\") {\n                const scopedMutations = this.#scopes.get(scope);\n                if (scopedMutations) {\n                    if (scopedMutations.length > 1) {\n                        const index = scopedMutations.indexOf(mutation);\n                        if (index !== -1) {\n                            scopedMutations.splice(index, 1);\n                        }\n                    } else if (scopedMutations[0] === mutation) {\n                        this.#scopes.delete(scope);\n                    }\n                }\n            }\n        }\n        this.notify({\n            type: \"removed\",\n            mutation\n        });\n    }\n    canRun(mutation) {\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const mutationsWithSameScope = this.#scopes.get(scope);\n            const firstPendingMutation = mutationsWithSameScope?.find((m)=>m.state.status === \"pending\");\n            return !firstPendingMutation || firstPendingMutation === mutation;\n        } else {\n            return true;\n        }\n    }\n    runNext(mutation) {\n        const scope = scopeFor(mutation);\n        if (typeof scope === \"string\") {\n            const foundMutation = this.#scopes.get(scope)?.find((m)=>m !== mutation && m.state.isPaused);\n            return foundMutation?.continue() ?? Promise.resolve();\n        } else {\n            return Promise.resolve();\n        }\n    }\n    clear() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.#mutations.forEach((mutation)=>{\n                this.notify({\n                    type: \"removed\",\n                    mutation\n                });\n            });\n            this.#mutations.clear();\n            this.#scopes.clear();\n        });\n    }\n    getAll() {\n        return Array.from(this.#mutations);\n    }\n    find(filters) {\n        const defaultedFilters = {\n            exact: true,\n            ...filters\n        };\n        return this.getAll().find((mutation)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(defaultedFilters, mutation));\n    }\n    findAll(filters = {}) {\n        return this.getAll().filter((mutation)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n    }\n    notify(event) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>{\n            this.listeners.forEach((listener)=>{\n                listener(event);\n            });\n        });\n    }\n    resumePausedMutations() {\n        const pausedMutations = this.getAll().filter((x)=>x.state.isPaused);\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(()=>Promise.all(pausedMutations.map((mutation)=>mutation.continue().catch(_utils_js__WEBPACK_IMPORTED_MODULE_3__.noop))));\n    }\n};\nfunction scopeFor(mutation) {\n    return mutation.options.scope?.id;\n}\n //# sourceMappingURL=mutationCache.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   defaultScheduler: () => (/* binding */ defaultScheduler),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n// src/notifyManager.ts\nvar defaultScheduler = (cb)=>setTimeout(cb, 0);\nfunction createNotifyManager() {\n    let queue = [];\n    let transactions = 0;\n    let notifyFn = (callback)=>{\n        callback();\n    };\n    let batchNotifyFn = (callback)=>{\n        callback();\n    };\n    let scheduleFn = defaultScheduler;\n    const schedule = (callback)=>{\n        if (transactions) {\n            queue.push(callback);\n        } else {\n            scheduleFn(()=>{\n                notifyFn(callback);\n            });\n        }\n    };\n    const flush = ()=>{\n        const originalQueue = queue;\n        queue = [];\n        if (originalQueue.length) {\n            scheduleFn(()=>{\n                batchNotifyFn(()=>{\n                    originalQueue.forEach((callback)=>{\n                        notifyFn(callback);\n                    });\n                });\n            });\n        }\n    };\n    return {\n        batch: (callback)=>{\n            let result;\n            transactions++;\n            try {\n                result = callback();\n            } finally{\n                transactions--;\n                if (!transactions) {\n                    flush();\n                }\n            }\n            return result;\n        },\n        /**\n     * All calls to the wrapped function will be batched.\n     */ batchCalls: (callback)=>{\n            return (...args)=>{\n                schedule(()=>{\n                    callback(...args);\n                });\n            };\n        },\n        schedule,\n        /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */ setNotifyFunction: (fn)=>{\n            notifyFn = fn;\n        },\n        /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */ setBatchNotifyFunction: (fn)=>{\n            batchNotifyFn = fn;\n        },\n        setScheduler: (fn)=>{\n            scheduleFn = fn;\n        }\n    };\n}\nvar notifyManager = createNotifyManager();\n //# sourceMappingURL=notifyManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    #online;\n    #cleanup;\n    #setup;\n    constructor(){\n        super();\n        this.#online = true;\n        this.#setup = (onOnline)=>{\n            if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n                const onlineListener = ()=>onOnline(true);\n                const offlineListener = ()=>onOnline(false);\n                window.addEventListener(\"online\", onlineListener, false);\n                window.addEventListener(\"offline\", offlineListener, false);\n                return ()=>{\n                    window.removeEventListener(\"online\", onlineListener);\n                    window.removeEventListener(\"offline\", offlineListener);\n                };\n            }\n            return;\n        };\n    }\n    onSubscribe() {\n        if (!this.#cleanup) {\n            this.setEventListener(this.#setup);\n        }\n    }\n    onUnsubscribe() {\n        if (!this.hasListeners()) {\n            this.#cleanup?.();\n            this.#cleanup = void 0;\n        }\n    }\n    setEventListener(setup) {\n        this.#setup = setup;\n        this.#cleanup?.();\n        this.#cleanup = setup(this.setOnline.bind(this));\n    }\n    setOnline(online) {\n        const changed = this.#online !== online;\n        if (changed) {\n            this.#online = online;\n            this.listeners.forEach((listener)=>{\n                listener(online);\n            });\n        }\n    }\n    isOnline() {\n        return this.#online;\n    }\n};\nvar onlineManager = new OnlineManager();\n //# sourceMappingURL=onlineManager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n    #initialState;\n    #revertState;\n    #cache;\n    #client;\n    #retryer;\n    #defaultOptions;\n    #abortSignalConsumed;\n    constructor(config){\n        super();\n        this.#abortSignalConsumed = false;\n        this.#defaultOptions = config.defaultOptions;\n        this.setOptions(config.options);\n        this.observers = [];\n        this.#client = config.client;\n        this.#cache = this.#client.getQueryCache();\n        this.queryKey = config.queryKey;\n        this.queryHash = config.queryHash;\n        this.#initialState = getDefaultState(this.options);\n        this.state = config.state ?? this.#initialState;\n        this.scheduleGc();\n    }\n    get meta() {\n        return this.options.meta;\n    }\n    get promise() {\n        return this.#retryer?.promise;\n    }\n    setOptions(options) {\n        this.options = {\n            ...this.#defaultOptions,\n            ...options\n        };\n        this.updateGcTime(this.options.gcTime);\n    }\n    optionalRemove() {\n        if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n            this.#cache.remove(this);\n        }\n    }\n    setData(newData, options) {\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n        this.#dispatch({\n            data,\n            type: \"success\",\n            dataUpdatedAt: options?.updatedAt,\n            manual: options?.manual\n        });\n        return data;\n    }\n    setState(state, setStateOptions) {\n        this.#dispatch({\n            type: \"setState\",\n            state,\n            setStateOptions\n        });\n    }\n    cancel(options) {\n        const promise = this.#retryer?.promise;\n        this.#retryer?.cancel(options);\n        return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n    }\n    destroy() {\n        super.destroy();\n        this.cancel({\n            silent: true\n        });\n    }\n    reset() {\n        this.destroy();\n        this.setState(this.#initialState);\n    }\n    isActive() {\n        return this.observers.some((observer)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false);\n    }\n    isDisabled() {\n        if (this.getObserversCount() > 0) {\n            return !this.isActive();\n        }\n        return this.options.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_1__.skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n    }\n    isStatic() {\n        if (this.getObserversCount() > 0) {\n            return this.observers.some((observer)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(observer.options.staleTime, this) === \"static\");\n        }\n        return false;\n    }\n    isStale() {\n        if (this.getObserversCount() > 0) {\n            return this.observers.some((observer)=>observer.getCurrentResult().isStale);\n        }\n        return this.state.data === void 0 || this.state.isInvalidated;\n    }\n    isStaleByTime(staleTime = 0) {\n        if (this.state.data === void 0) {\n            return true;\n        }\n        if (staleTime === \"static\") {\n            return false;\n        }\n        if (this.state.isInvalidated) {\n            return true;\n        }\n        return !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n    }\n    onFocus() {\n        const observer = this.observers.find((x)=>x.shouldFetchOnWindowFocus());\n        observer?.refetch({\n            cancelRefetch: false\n        });\n        this.#retryer?.continue();\n    }\n    onOnline() {\n        const observer = this.observers.find((x)=>x.shouldFetchOnReconnect());\n        observer?.refetch({\n            cancelRefetch: false\n        });\n        this.#retryer?.continue();\n    }\n    addObserver(observer) {\n        if (!this.observers.includes(observer)) {\n            this.observers.push(observer);\n            this.clearGcTimeout();\n            this.#cache.notify({\n                type: \"observerAdded\",\n                query: this,\n                observer\n            });\n        }\n    }\n    removeObserver(observer) {\n        if (this.observers.includes(observer)) {\n            this.observers = this.observers.filter((x)=>x !== observer);\n            if (!this.observers.length) {\n                if (this.#retryer) {\n                    if (this.#abortSignalConsumed) {\n                        this.#retryer.cancel({\n                            revert: true\n                        });\n                    } else {\n                        this.#retryer.cancelRetry();\n                    }\n                }\n                this.scheduleGc();\n            }\n            this.#cache.notify({\n                type: \"observerRemoved\",\n                query: this,\n                observer\n            });\n        }\n    }\n    getObserversCount() {\n        return this.observers.length;\n    }\n    invalidate() {\n        if (!this.state.isInvalidated) {\n            this.#dispatch({\n                type: \"invalidate\"\n            });\n        }\n    }\n    fetch(options, fetchOptions) {\n        if (this.state.fetchStatus !== \"idle\") {\n            if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n                this.cancel({\n                    silent: true\n                });\n            } else if (this.#retryer) {\n                this.#retryer.continueRetry();\n                return this.#retryer.promise;\n            }\n        }\n        if (options) {\n            this.setOptions(options);\n        }\n        if (!this.options.queryFn) {\n            const observer = this.observers.find((x)=>x.options.queryFn);\n            if (observer) {\n                this.setOptions(observer.options);\n            }\n        }\n        if (true) {\n            if (!Array.isArray(this.options.queryKey)) {\n                console.error(`As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`);\n            }\n        }\n        const abortController = new AbortController();\n        const addSignalProperty = (object)=>{\n            Object.defineProperty(object, \"signal\", {\n                enumerable: true,\n                get: ()=>{\n                    this.#abortSignalConsumed = true;\n                    return abortController.signal;\n                }\n            });\n        };\n        const fetchFn = ()=>{\n            const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n            const createQueryFnContext = ()=>{\n                const queryFnContext2 = {\n                    client: this.#client,\n                    queryKey: this.queryKey,\n                    meta: this.meta\n                };\n                addSignalProperty(queryFnContext2);\n                return queryFnContext2;\n            };\n            const queryFnContext = createQueryFnContext();\n            this.#abortSignalConsumed = false;\n            if (this.options.persister) {\n                return this.options.persister(queryFn, queryFnContext, this);\n            }\n            return queryFn(queryFnContext);\n        };\n        const createFetchContext = ()=>{\n            const context2 = {\n                fetchOptions,\n                options: this.options,\n                queryKey: this.queryKey,\n                client: this.#client,\n                state: this.state,\n                fetchFn\n            };\n            addSignalProperty(context2);\n            return context2;\n        };\n        const context = createFetchContext();\n        this.options.behavior?.onFetch(context, this);\n        this.#revertState = this.state;\n        if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n            this.#dispatch({\n                type: \"fetch\",\n                meta: context.fetchOptions?.meta\n            });\n        }\n        const onError = (error)=>{\n            if (!((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n                this.#dispatch({\n                    type: \"error\",\n                    error\n                });\n            }\n            if (!(0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n                this.#cache.config.onError?.(error, this);\n                this.#cache.config.onSettled?.(this.state.data, error, this);\n            }\n            this.scheduleGc();\n        };\n        this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n            initialPromise: fetchOptions?.initialPromise,\n            fn: context.fetchFn,\n            abort: abortController.abort.bind(abortController),\n            onSuccess: (data)=>{\n                if (data === void 0) {\n                    if (true) {\n                        console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`);\n                    }\n                    onError(new Error(`${this.queryHash} data is undefined`));\n                    return;\n                }\n                try {\n                    this.setData(data);\n                } catch (error) {\n                    onError(error);\n                    return;\n                }\n                this.#cache.config.onSuccess?.(data, this);\n                this.#cache.config.onSettled?.(data, this.state.error, this);\n                this.scheduleGc();\n            },\n            onError,\n            onFail: (failureCount, error)=>{\n                this.#dispatch({\n                    type: \"failed\",\n                    failureCount,\n                    error\n                });\n            },\n            onPause: ()=>{\n                this.#dispatch({\n                    type: \"pause\"\n                });\n            },\n            onContinue: ()=>{\n                this.#dispatch({\n                    type: \"continue\"\n                });\n            },\n            retry: context.options.retry,\n            retryDelay: context.options.retryDelay,\n            networkMode: context.options.networkMode,\n            canRun: ()=>true\n        });\n        return this.#retryer.start();\n    }\n    #dispatch(action) {\n        const reducer = (state)=>{\n            switch(action.type){\n                case \"failed\":\n                    return {\n                        ...state,\n                        fetchFailureCount: action.failureCount,\n                        fetchFailureReason: action.error\n                    };\n                case \"pause\":\n                    return {\n                        ...state,\n                        fetchStatus: \"paused\"\n                    };\n                case \"continue\":\n                    return {\n                        ...state,\n                        fetchStatus: \"fetching\"\n                    };\n                case \"fetch\":\n                    return {\n                        ...state,\n                        ...fetchState(state.data, this.options),\n                        fetchMeta: action.meta ?? null\n                    };\n                case \"success\":\n                    return {\n                        ...state,\n                        data: action.data,\n                        dataUpdateCount: state.dataUpdateCount + 1,\n                        dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n                        error: null,\n                        isInvalidated: false,\n                        status: \"success\",\n                        ...!action.manual && {\n                            fetchStatus: \"idle\",\n                            fetchFailureCount: 0,\n                            fetchFailureReason: null\n                        }\n                    };\n                case \"error\":\n                    const error = action.error;\n                    if ((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.#revertState) {\n                        return {\n                            ...this.#revertState,\n                            fetchStatus: \"idle\"\n                        };\n                    }\n                    return {\n                        ...state,\n                        error,\n                        errorUpdateCount: state.errorUpdateCount + 1,\n                        errorUpdatedAt: Date.now(),\n                        fetchFailureCount: state.fetchFailureCount + 1,\n                        fetchFailureReason: error,\n                        fetchStatus: \"idle\",\n                        status: \"error\"\n                    };\n                case \"invalidate\":\n                    return {\n                        ...state,\n                        isInvalidated: true\n                    };\n                case \"setState\":\n                    return {\n                        ...state,\n                        ...action.state\n                    };\n            }\n        };\n        this.state = reducer(this.state);\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.observers.forEach((observer)=>{\n                observer.onQueryUpdate();\n            });\n            this.#cache.notify({\n                query: this,\n                type: \"updated\",\n                action\n            });\n        });\n    }\n};\nfunction fetchState(data, options) {\n    return {\n        fetchFailureCount: 0,\n        fetchFailureReason: null,\n        fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n        ...data === void 0 && {\n            error: null,\n            status: \"pending\"\n        }\n    };\n}\nfunction getDefaultState(options) {\n    const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n    const hasData = data !== void 0;\n    const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    return {\n        data,\n        dataUpdateCount: 0,\n        dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n        error: null,\n        errorUpdateCount: 0,\n        errorUpdatedAt: 0,\n        fetchFailureCount: 0,\n        fetchFailureReason: null,\n        fetchMeta: null,\n        isInvalidated: false,\n        status: hasData ? \"success\" : \"pending\",\n        fetchStatus: \"idle\"\n    };\n}\n //# sourceMappingURL=query.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3F1ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLGVBQWU7QUFTSztBQUMrQjtBQUNzQjtBQUM5QjtBQUMzQyxJQUFJWSxRQUFRLGNBQWNELG9EQUFTQTtJQUNqQyxDQUFDRSxZQUFZLENBQUM7SUFDZCxDQUFDQyxXQUFXLENBQUM7SUFDYixDQUFDQyxLQUFLLENBQUM7SUFDUCxDQUFDQyxNQUFNLENBQUM7SUFDUixDQUFDQyxPQUFPLENBQUM7SUFDVCxDQUFDQyxjQUFjLENBQUM7SUFDaEIsQ0FBQ0MsbUJBQW1CLENBQUM7SUFDckJDLFlBQVlDLE1BQU0sQ0FBRTtRQUNsQixLQUFLO1FBQ0wsSUFBSSxDQUFDLENBQUNGLG1CQUFtQixHQUFHO1FBQzVCLElBQUksQ0FBQyxDQUFDRCxjQUFjLEdBQUdHLE9BQU9ILGNBQWM7UUFDNUMsSUFBSSxDQUFDSSxVQUFVLENBQUNELE9BQU9FLE9BQU87UUFDOUIsSUFBSSxDQUFDQyxTQUFTLEdBQUcsRUFBRTtRQUNuQixJQUFJLENBQUMsQ0FBQ1IsTUFBTSxHQUFHSyxPQUFPTCxNQUFNO1FBQzVCLElBQUksQ0FBQyxDQUFDRCxLQUFLLEdBQUcsSUFBSSxDQUFDLENBQUNDLE1BQU0sQ0FBQ1MsYUFBYTtRQUN4QyxJQUFJLENBQUNDLFFBQVEsR0FBR0wsT0FBT0ssUUFBUTtRQUMvQixJQUFJLENBQUNDLFNBQVMsR0FBR04sT0FBT00sU0FBUztRQUNqQyxJQUFJLENBQUMsQ0FBQ2QsWUFBWSxHQUFHZSxnQkFBZ0IsSUFBSSxDQUFDTCxPQUFPO1FBQ2pELElBQUksQ0FBQ00sS0FBSyxHQUFHUixPQUFPUSxLQUFLLElBQUksSUFBSSxDQUFDLENBQUNoQixZQUFZO1FBQy9DLElBQUksQ0FBQ2lCLFVBQVU7SUFDakI7SUFDQSxJQUFJQyxPQUFPO1FBQ1QsT0FBTyxJQUFJLENBQUNSLE9BQU8sQ0FBQ1EsSUFBSTtJQUMxQjtJQUNBLElBQUlDLFVBQVU7UUFDWixPQUFPLElBQUksQ0FBQyxDQUFDZixPQUFPLEVBQUVlO0lBQ3hCO0lBQ0FWLFdBQVdDLE9BQU8sRUFBRTtRQUNsQixJQUFJLENBQUNBLE9BQU8sR0FBRztZQUFFLEdBQUcsSUFBSSxDQUFDLENBQUNMLGNBQWM7WUFBRSxHQUFHSyxPQUFPO1FBQUM7UUFDckQsSUFBSSxDQUFDVSxZQUFZLENBQUMsSUFBSSxDQUFDVixPQUFPLENBQUNXLE1BQU07SUFDdkM7SUFDQUMsaUJBQWlCO1FBQ2YsSUFBSSxDQUFDLElBQUksQ0FBQ1gsU0FBUyxDQUFDWSxNQUFNLElBQUksSUFBSSxDQUFDUCxLQUFLLENBQUNRLFdBQVcsS0FBSyxRQUFRO1lBQy9ELElBQUksQ0FBQyxDQUFDdEIsS0FBSyxDQUFDdUIsTUFBTSxDQUFDLElBQUk7UUFDekI7SUFDRjtJQUNBQyxRQUFRQyxPQUFPLEVBQUVqQixPQUFPLEVBQUU7UUFDeEIsTUFBTWtCLE9BQU92QyxzREFBV0EsQ0FBQyxJQUFJLENBQUMyQixLQUFLLENBQUNZLElBQUksRUFBRUQsU0FBUyxJQUFJLENBQUNqQixPQUFPO1FBQy9ELElBQUksQ0FBQyxDQUFDbUIsUUFBUSxDQUFDO1lBQ2JEO1lBQ0FFLE1BQU07WUFDTkMsZUFBZXJCLFNBQVNzQjtZQUN4QkMsUUFBUXZCLFNBQVN1QjtRQUNuQjtRQUNBLE9BQU9MO0lBQ1Q7SUFDQU0sU0FBU2xCLEtBQUssRUFBRW1CLGVBQWUsRUFBRTtRQUMvQixJQUFJLENBQUMsQ0FBQ04sUUFBUSxDQUFDO1lBQUVDLE1BQU07WUFBWWQ7WUFBT21CO1FBQWdCO0lBQzVEO0lBQ0FDLE9BQU8xQixPQUFPLEVBQUU7UUFDZCxNQUFNUyxVQUFVLElBQUksQ0FBQyxDQUFDZixPQUFPLEVBQUVlO1FBQy9CLElBQUksQ0FBQyxDQUFDZixPQUFPLEVBQUVnQyxPQUFPMUI7UUFDdEIsT0FBT1MsVUFBVUEsUUFBUWtCLElBQUksQ0FBQ2pELDJDQUFJQSxFQUFFa0QsS0FBSyxDQUFDbEQsMkNBQUlBLElBQUltRCxRQUFRQyxPQUFPO0lBQ25FO0lBQ0FDLFVBQVU7UUFDUixLQUFLLENBQUNBO1FBQ04sSUFBSSxDQUFDTCxNQUFNLENBQUM7WUFBRU0sUUFBUTtRQUFLO0lBQzdCO0lBQ0FDLFFBQVE7UUFDTixJQUFJLENBQUNGLE9BQU87UUFDWixJQUFJLENBQUNQLFFBQVEsQ0FBQyxJQUFJLENBQUMsQ0FBQ2xDLFlBQVk7SUFDbEM7SUFDQTRDLFdBQVc7UUFDVCxPQUFPLElBQUksQ0FBQ2pDLFNBQVMsQ0FBQ2tDLElBQUksQ0FDeEIsQ0FBQ0MsV0FBYXhELHlEQUFjQSxDQUFDd0QsU0FBU3BDLE9BQU8sQ0FBQ3FDLE9BQU8sRUFBRSxJQUFJLE1BQU07SUFFckU7SUFDQUMsYUFBYTtRQUNYLElBQUksSUFBSSxDQUFDQyxpQkFBaUIsS0FBSyxHQUFHO1lBQ2hDLE9BQU8sQ0FBQyxJQUFJLENBQUNMLFFBQVE7UUFDdkI7UUFDQSxPQUFPLElBQUksQ0FBQ2xDLE9BQU8sQ0FBQ3dDLE9BQU8sS0FBSzFELGdEQUFTQSxJQUFJLElBQUksQ0FBQ3dCLEtBQUssQ0FBQ21DLGVBQWUsR0FBRyxJQUFJLENBQUNuQyxLQUFLLENBQUNvQyxnQkFBZ0IsS0FBSztJQUM1RztJQUNBQyxXQUFXO1FBQ1QsSUFBSSxJQUFJLENBQUNKLGlCQUFpQixLQUFLLEdBQUc7WUFDaEMsT0FBTyxJQUFJLENBQUN0QyxTQUFTLENBQUNrQyxJQUFJLENBQ3hCLENBQUNDLFdBQWF2RCwyREFBZ0JBLENBQUN1RCxTQUFTcEMsT0FBTyxDQUFDNEMsU0FBUyxFQUFFLElBQUksTUFBTTtRQUV6RTtRQUNBLE9BQU87SUFDVDtJQUNBQyxVQUFVO1FBQ1IsSUFBSSxJQUFJLENBQUNOLGlCQUFpQixLQUFLLEdBQUc7WUFDaEMsT0FBTyxJQUFJLENBQUN0QyxTQUFTLENBQUNrQyxJQUFJLENBQ3hCLENBQUNDLFdBQWFBLFNBQVNVLGdCQUFnQixHQUFHRCxPQUFPO1FBRXJEO1FBQ0EsT0FBTyxJQUFJLENBQUN2QyxLQUFLLENBQUNZLElBQUksS0FBSyxLQUFLLEtBQUssSUFBSSxDQUFDWixLQUFLLENBQUN5QyxhQUFhO0lBQy9EO0lBQ0FDLGNBQWNKLFlBQVksQ0FBQyxFQUFFO1FBQzNCLElBQUksSUFBSSxDQUFDdEMsS0FBSyxDQUFDWSxJQUFJLEtBQUssS0FBSyxHQUFHO1lBQzlCLE9BQU87UUFDVDtRQUNBLElBQUkwQixjQUFjLFVBQVU7WUFDMUIsT0FBTztRQUNUO1FBQ0EsSUFBSSxJQUFJLENBQUN0QyxLQUFLLENBQUN5QyxhQUFhLEVBQUU7WUFDNUIsT0FBTztRQUNUO1FBQ0EsT0FBTyxDQUFDaEUseURBQWNBLENBQUMsSUFBSSxDQUFDdUIsS0FBSyxDQUFDZSxhQUFhLEVBQUV1QjtJQUNuRDtJQUNBSyxVQUFVO1FBQ1IsTUFBTWIsV0FBVyxJQUFJLENBQUNuQyxTQUFTLENBQUNpRCxJQUFJLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRUMsd0JBQXdCO1FBQ3RFaEIsVUFBVWlCLFFBQVE7WUFBRUMsZUFBZTtRQUFNO1FBQ3pDLElBQUksQ0FBQyxDQUFDNUQsT0FBTyxFQUFFNkQ7SUFDakI7SUFDQUMsV0FBVztRQUNULE1BQU1wQixXQUFXLElBQUksQ0FBQ25DLFNBQVMsQ0FBQ2lELElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFTSxzQkFBc0I7UUFDcEVyQixVQUFVaUIsUUFBUTtZQUFFQyxlQUFlO1FBQU07UUFDekMsSUFBSSxDQUFDLENBQUM1RCxPQUFPLEVBQUU2RDtJQUNqQjtJQUNBRyxZQUFZdEIsUUFBUSxFQUFFO1FBQ3BCLElBQUksQ0FBQyxJQUFJLENBQUNuQyxTQUFTLENBQUMwRCxRQUFRLENBQUN2QixXQUFXO1lBQ3RDLElBQUksQ0FBQ25DLFNBQVMsQ0FBQzJELElBQUksQ0FBQ3hCO1lBQ3BCLElBQUksQ0FBQ3lCLGNBQWM7WUFDbkIsSUFBSSxDQUFDLENBQUNyRSxLQUFLLENBQUNzRSxNQUFNLENBQUM7Z0JBQUUxQyxNQUFNO2dCQUFpQjJDLE9BQU8sSUFBSTtnQkFBRTNCO1lBQVM7UUFDcEU7SUFDRjtJQUNBNEIsZUFBZTVCLFFBQVEsRUFBRTtRQUN2QixJQUFJLElBQUksQ0FBQ25DLFNBQVMsQ0FBQzBELFFBQVEsQ0FBQ3ZCLFdBQVc7WUFDckMsSUFBSSxDQUFDbkMsU0FBUyxHQUFHLElBQUksQ0FBQ0EsU0FBUyxDQUFDZ0UsTUFBTSxDQUFDLENBQUNkLElBQU1BLE1BQU1mO1lBQ3BELElBQUksQ0FBQyxJQUFJLENBQUNuQyxTQUFTLENBQUNZLE1BQU0sRUFBRTtnQkFDMUIsSUFBSSxJQUFJLENBQUMsQ0FBQ25CLE9BQU8sRUFBRTtvQkFDakIsSUFBSSxJQUFJLENBQUMsQ0FBQ0UsbUJBQW1CLEVBQUU7d0JBQzdCLElBQUksQ0FBQyxDQUFDRixPQUFPLENBQUNnQyxNQUFNLENBQUM7NEJBQUV3QyxRQUFRO3dCQUFLO29CQUN0QyxPQUFPO3dCQUNMLElBQUksQ0FBQyxDQUFDeEUsT0FBTyxDQUFDeUUsV0FBVztvQkFDM0I7Z0JBQ0Y7Z0JBQ0EsSUFBSSxDQUFDNUQsVUFBVTtZQUNqQjtZQUNBLElBQUksQ0FBQyxDQUFDZixLQUFLLENBQUNzRSxNQUFNLENBQUM7Z0JBQUUxQyxNQUFNO2dCQUFtQjJDLE9BQU8sSUFBSTtnQkFBRTNCO1lBQVM7UUFDdEU7SUFDRjtJQUNBRyxvQkFBb0I7UUFDbEIsT0FBTyxJQUFJLENBQUN0QyxTQUFTLENBQUNZLE1BQU07SUFDOUI7SUFDQXVELGFBQWE7UUFDWCxJQUFJLENBQUMsSUFBSSxDQUFDOUQsS0FBSyxDQUFDeUMsYUFBYSxFQUFFO1lBQzdCLElBQUksQ0FBQyxDQUFDNUIsUUFBUSxDQUFDO2dCQUFFQyxNQUFNO1lBQWE7UUFDdEM7SUFDRjtJQUNBaUQsTUFBTXJFLE9BQU8sRUFBRXNFLFlBQVksRUFBRTtRQUMzQixJQUFJLElBQUksQ0FBQ2hFLEtBQUssQ0FBQ1EsV0FBVyxLQUFLLFFBQVE7WUFDckMsSUFBSSxJQUFJLENBQUNSLEtBQUssQ0FBQ1ksSUFBSSxLQUFLLEtBQUssS0FBS29ELGNBQWNoQixlQUFlO2dCQUM3RCxJQUFJLENBQUM1QixNQUFNLENBQUM7b0JBQUVNLFFBQVE7Z0JBQUs7WUFDN0IsT0FBTyxJQUFJLElBQUksQ0FBQyxDQUFDdEMsT0FBTyxFQUFFO2dCQUN4QixJQUFJLENBQUMsQ0FBQ0EsT0FBTyxDQUFDNkUsYUFBYTtnQkFDM0IsT0FBTyxJQUFJLENBQUMsQ0FBQzdFLE9BQU8sQ0FBQ2UsT0FBTztZQUM5QjtRQUNGO1FBQ0EsSUFBSVQsU0FBUztZQUNYLElBQUksQ0FBQ0QsVUFBVSxDQUFDQztRQUNsQjtRQUNBLElBQUksQ0FBQyxJQUFJLENBQUNBLE9BQU8sQ0FBQ3dDLE9BQU8sRUFBRTtZQUN6QixNQUFNSixXQUFXLElBQUksQ0FBQ25DLFNBQVMsQ0FBQ2lELElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFbkQsT0FBTyxDQUFDd0MsT0FBTztZQUM3RCxJQUFJSixVQUFVO2dCQUNaLElBQUksQ0FBQ3JDLFVBQVUsQ0FBQ3FDLFNBQVNwQyxPQUFPO1lBQ2xDO1FBQ0Y7UUFDQSxJQUFJd0UsSUFBcUMsRUFBRTtZQUN6QyxJQUFJLENBQUNDLE1BQU1DLE9BQU8sQ0FBQyxJQUFJLENBQUMxRSxPQUFPLENBQUNHLFFBQVEsR0FBRztnQkFDekN3RSxRQUFRQyxLQUFLLENBQ1gsQ0FBQyxtSUFBbUksQ0FBQztZQUV6STtRQUNGO1FBQ0EsTUFBTUMsa0JBQWtCLElBQUlDO1FBQzVCLE1BQU1DLG9CQUFvQixDQUFDQztZQUN6QkMsT0FBT0MsY0FBYyxDQUFDRixRQUFRLFVBQVU7Z0JBQ3RDRyxZQUFZO2dCQUNaQyxLQUFLO29CQUNILElBQUksQ0FBQyxDQUFDeEYsbUJBQW1CLEdBQUc7b0JBQzVCLE9BQU9pRixnQkFBZ0JRLE1BQU07Z0JBQy9CO1lBQ0Y7UUFDRjtRQUNBLE1BQU1DLFVBQVU7WUFDZCxNQUFNOUMsVUFBVS9ELHdEQUFhQSxDQUFDLElBQUksQ0FBQ3VCLE9BQU8sRUFBRXNFO1lBQzVDLE1BQU1pQix1QkFBdUI7Z0JBQzNCLE1BQU1DLGtCQUFrQjtvQkFDdEIvRixRQUFRLElBQUksQ0FBQyxDQUFDQSxNQUFNO29CQUNwQlUsVUFBVSxJQUFJLENBQUNBLFFBQVE7b0JBQ3ZCSyxNQUFNLElBQUksQ0FBQ0EsSUFBSTtnQkFDakI7Z0JBQ0F1RSxrQkFBa0JTO2dCQUNsQixPQUFPQTtZQUNUO1lBQ0EsTUFBTUMsaUJBQWlCRjtZQUN2QixJQUFJLENBQUMsQ0FBQzNGLG1CQUFtQixHQUFHO1lBQzVCLElBQUksSUFBSSxDQUFDSSxPQUFPLENBQUMwRixTQUFTLEVBQUU7Z0JBQzFCLE9BQU8sSUFBSSxDQUFDMUYsT0FBTyxDQUFDMEYsU0FBUyxDQUMzQmxELFNBQ0FpRCxnQkFDQSxJQUFJO1lBRVI7WUFDQSxPQUFPakQsUUFBUWlEO1FBQ2pCO1FBQ0EsTUFBTUUscUJBQXFCO1lBQ3pCLE1BQU1DLFdBQVc7Z0JBQ2Z0QjtnQkFDQXRFLFNBQVMsSUFBSSxDQUFDQSxPQUFPO2dCQUNyQkcsVUFBVSxJQUFJLENBQUNBLFFBQVE7Z0JBQ3ZCVixRQUFRLElBQUksQ0FBQyxDQUFDQSxNQUFNO2dCQUNwQmEsT0FBTyxJQUFJLENBQUNBLEtBQUs7Z0JBQ2pCZ0Y7WUFDRjtZQUNBUCxrQkFBa0JhO1lBQ2xCLE9BQU9BO1FBQ1Q7UUFDQSxNQUFNQyxVQUFVRjtRQUNoQixJQUFJLENBQUMzRixPQUFPLENBQUM4RixRQUFRLEVBQUVDLFFBQVFGLFNBQVMsSUFBSTtRQUM1QyxJQUFJLENBQUMsQ0FBQ3RHLFdBQVcsR0FBRyxJQUFJLENBQUNlLEtBQUs7UUFDOUIsSUFBSSxJQUFJLENBQUNBLEtBQUssQ0FBQ1EsV0FBVyxLQUFLLFVBQVUsSUFBSSxDQUFDUixLQUFLLENBQUMwRixTQUFTLEtBQUtILFFBQVF2QixZQUFZLEVBQUU5RCxNQUFNO1lBQzVGLElBQUksQ0FBQyxDQUFDVyxRQUFRLENBQUM7Z0JBQUVDLE1BQU07Z0JBQVNaLE1BQU1xRixRQUFRdkIsWUFBWSxFQUFFOUQ7WUFBSztRQUNuRTtRQUNBLE1BQU15RixVQUFVLENBQUNyQjtZQUNmLElBQUksQ0FBRXpGLENBQUFBLDZEQUFnQkEsQ0FBQ3lGLFVBQVVBLE1BQU01QyxNQUFNLEdBQUc7Z0JBQzlDLElBQUksQ0FBQyxDQUFDYixRQUFRLENBQUM7b0JBQ2JDLE1BQU07b0JBQ053RDtnQkFDRjtZQUNGO1lBQ0EsSUFBSSxDQUFDekYsNkRBQWdCQSxDQUFDeUYsUUFBUTtnQkFDNUIsSUFBSSxDQUFDLENBQUNwRixLQUFLLENBQUNNLE1BQU0sQ0FBQ21HLE9BQU8sR0FDeEJyQixPQUNBLElBQUk7Z0JBRU4sSUFBSSxDQUFDLENBQUNwRixLQUFLLENBQUNNLE1BQU0sQ0FBQ29HLFNBQVMsR0FDMUIsSUFBSSxDQUFDNUYsS0FBSyxDQUFDWSxJQUFJLEVBQ2YwRCxPQUNBLElBQUk7WUFFUjtZQUNBLElBQUksQ0FBQ3JFLFVBQVU7UUFDakI7UUFDQSxJQUFJLENBQUMsQ0FBQ2IsT0FBTyxHQUFHUiwwREFBYUEsQ0FBQztZQUM1QmlILGdCQUFnQjdCLGNBQWM2QjtZQUM5QkMsSUFBSVAsUUFBUVAsT0FBTztZQUNuQmUsT0FBT3hCLGdCQUFnQndCLEtBQUssQ0FBQ0MsSUFBSSxDQUFDekI7WUFDbEMwQixXQUFXLENBQUNyRjtnQkFDVixJQUFJQSxTQUFTLEtBQUssR0FBRztvQkFDbkIsSUFBSXNELElBQXFDLEVBQUU7d0JBQ3pDRyxRQUFRQyxLQUFLLENBQ1gsQ0FBQyxzSUFBc0ksRUFBRSxJQUFJLENBQUN4RSxTQUFTLENBQUMsQ0FBQztvQkFFN0o7b0JBQ0E2RixRQUFRLElBQUlPLE1BQU0sQ0FBQyxFQUFFLElBQUksQ0FBQ3BHLFNBQVMsQ0FBQyxrQkFBa0IsQ0FBQztvQkFDdkQ7Z0JBQ0Y7Z0JBQ0EsSUFBSTtvQkFDRixJQUFJLENBQUNZLE9BQU8sQ0FBQ0U7Z0JBQ2YsRUFBRSxPQUFPMEQsT0FBTztvQkFDZHFCLFFBQVFyQjtvQkFDUjtnQkFDRjtnQkFDQSxJQUFJLENBQUMsQ0FBQ3BGLEtBQUssQ0FBQ00sTUFBTSxDQUFDeUcsU0FBUyxHQUFHckYsTUFBTSxJQUFJO2dCQUN6QyxJQUFJLENBQUMsQ0FBQzFCLEtBQUssQ0FBQ00sTUFBTSxDQUFDb0csU0FBUyxHQUMxQmhGLE1BQ0EsSUFBSSxDQUFDWixLQUFLLENBQUNzRSxLQUFLLEVBQ2hCLElBQUk7Z0JBRU4sSUFBSSxDQUFDckUsVUFBVTtZQUNqQjtZQUNBMEY7WUFDQVEsUUFBUSxDQUFDQyxjQUFjOUI7Z0JBQ3JCLElBQUksQ0FBQyxDQUFDekQsUUFBUSxDQUFDO29CQUFFQyxNQUFNO29CQUFVc0Y7b0JBQWM5QjtnQkFBTTtZQUN2RDtZQUNBK0IsU0FBUztnQkFDUCxJQUFJLENBQUMsQ0FBQ3hGLFFBQVEsQ0FBQztvQkFBRUMsTUFBTTtnQkFBUTtZQUNqQztZQUNBd0YsWUFBWTtnQkFDVixJQUFJLENBQUMsQ0FBQ3pGLFFBQVEsQ0FBQztvQkFBRUMsTUFBTTtnQkFBVztZQUNwQztZQUNBeUYsT0FBT2hCLFFBQVE3RixPQUFPLENBQUM2RyxLQUFLO1lBQzVCQyxZQUFZakIsUUFBUTdGLE9BQU8sQ0FBQzhHLFVBQVU7WUFDdENDLGFBQWFsQixRQUFRN0YsT0FBTyxDQUFDK0csV0FBVztZQUN4Q0MsUUFBUSxJQUFNO1FBQ2hCO1FBQ0EsT0FBTyxJQUFJLENBQUMsQ0FBQ3RILE9BQU8sQ0FBQ3VILEtBQUs7SUFDNUI7SUFDQSxDQUFDOUYsUUFBUSxDQUFDK0YsTUFBTTtRQUNkLE1BQU1DLFVBQVUsQ0FBQzdHO1lBQ2YsT0FBUTRHLE9BQU85RixJQUFJO2dCQUNqQixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR2QsS0FBSzt3QkFDUjhHLG1CQUFtQkYsT0FBT1IsWUFBWTt3QkFDdENXLG9CQUFvQkgsT0FBT3RDLEtBQUs7b0JBQ2xDO2dCQUNGLEtBQUs7b0JBQ0gsT0FBTzt3QkFDTCxHQUFHdEUsS0FBSzt3QkFDUlEsYUFBYTtvQkFDZjtnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR1IsS0FBSzt3QkFDUlEsYUFBYTtvQkFDZjtnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR1IsS0FBSzt3QkFDUixHQUFHZ0gsV0FBV2hILE1BQU1ZLElBQUksRUFBRSxJQUFJLENBQUNsQixPQUFPLENBQUM7d0JBQ3ZDZ0csV0FBV2tCLE9BQU8xRyxJQUFJLElBQUk7b0JBQzVCO2dCQUNGLEtBQUs7b0JBQ0gsT0FBTzt3QkFDTCxHQUFHRixLQUFLO3dCQUNSWSxNQUFNZ0csT0FBT2hHLElBQUk7d0JBQ2pCdUIsaUJBQWlCbkMsTUFBTW1DLGVBQWUsR0FBRzt3QkFDekNwQixlQUFlNkYsT0FBTzdGLGFBQWEsSUFBSWtHLEtBQUtDLEdBQUc7d0JBQy9DNUMsT0FBTzt3QkFDUDdCLGVBQWU7d0JBQ2YwRSxRQUFRO3dCQUNSLEdBQUcsQ0FBQ1AsT0FBTzNGLE1BQU0sSUFBSTs0QkFDbkJULGFBQWE7NEJBQ2JzRyxtQkFBbUI7NEJBQ25CQyxvQkFBb0I7d0JBQ3RCLENBQUM7b0JBQ0g7Z0JBQ0YsS0FBSztvQkFDSCxNQUFNekMsUUFBUXNDLE9BQU90QyxLQUFLO29CQUMxQixJQUFJekYsNkRBQWdCQSxDQUFDeUYsVUFBVUEsTUFBTVYsTUFBTSxJQUFJLElBQUksQ0FBQyxDQUFDM0UsV0FBVyxFQUFFO3dCQUNoRSxPQUFPOzRCQUFFLEdBQUcsSUFBSSxDQUFDLENBQUNBLFdBQVc7NEJBQUV1QixhQUFhO3dCQUFPO29CQUNyRDtvQkFDQSxPQUFPO3dCQUNMLEdBQUdSLEtBQUs7d0JBQ1JzRTt3QkFDQWxDLGtCQUFrQnBDLE1BQU1vQyxnQkFBZ0IsR0FBRzt3QkFDM0NnRixnQkFBZ0JILEtBQUtDLEdBQUc7d0JBQ3hCSixtQkFBbUI5RyxNQUFNOEcsaUJBQWlCLEdBQUc7d0JBQzdDQyxvQkFBb0J6Qzt3QkFDcEI5RCxhQUFhO3dCQUNiMkcsUUFBUTtvQkFDVjtnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR25ILEtBQUs7d0JBQ1J5QyxlQUFlO29CQUNqQjtnQkFDRixLQUFLO29CQUNILE9BQU87d0JBQ0wsR0FBR3pDLEtBQUs7d0JBQ1IsR0FBRzRHLE9BQU81RyxLQUFLO29CQUNqQjtZQUNKO1FBQ0Y7UUFDQSxJQUFJLENBQUNBLEtBQUssR0FBRzZHLFFBQVEsSUFBSSxDQUFDN0csS0FBSztRQUMvQnRCLDREQUFhQSxDQUFDMkksS0FBSyxDQUFDO1lBQ2xCLElBQUksQ0FBQzFILFNBQVMsQ0FBQzJILE9BQU8sQ0FBQyxDQUFDeEY7Z0JBQ3RCQSxTQUFTeUYsYUFBYTtZQUN4QjtZQUNBLElBQUksQ0FBQyxDQUFDckksS0FBSyxDQUFDc0UsTUFBTSxDQUFDO2dCQUFFQyxPQUFPLElBQUk7Z0JBQUUzQyxNQUFNO2dCQUFXOEY7WUFBTztRQUM1RDtJQUNGO0FBQ0Y7QUFDQSxTQUFTSSxXQUFXcEcsSUFBSSxFQUFFbEIsT0FBTztJQUMvQixPQUFPO1FBQ0xvSCxtQkFBbUI7UUFDbkJDLG9CQUFvQjtRQUNwQnZHLGFBQWE3QixxREFBUUEsQ0FBQ2UsUUFBUStHLFdBQVcsSUFBSSxhQUFhO1FBQzFELEdBQUc3RixTQUFTLEtBQUssS0FBSztZQUNwQjBELE9BQU87WUFDUDZDLFFBQVE7UUFDVixDQUFDO0lBQ0g7QUFDRjtBQUNBLFNBQVNwSCxnQkFBZ0JMLE9BQU87SUFDOUIsTUFBTWtCLE9BQU8sT0FBT2xCLFFBQVE4SCxXQUFXLEtBQUssYUFBYTlILFFBQVE4SCxXQUFXLEtBQUs5SCxRQUFROEgsV0FBVztJQUNwRyxNQUFNQyxVQUFVN0csU0FBUyxLQUFLO0lBQzlCLE1BQU04Ryx1QkFBdUJELFVBQVUsT0FBTy9ILFFBQVFnSSxvQkFBb0IsS0FBSyxhQUFhaEksUUFBUWdJLG9CQUFvQixLQUFLaEksUUFBUWdJLG9CQUFvQixHQUFHO0lBQzVKLE9BQU87UUFDTDlHO1FBQ0F1QixpQkFBaUI7UUFDakJwQixlQUFlMEcsVUFBVUMsd0JBQXdCVCxLQUFLQyxHQUFHLEtBQUs7UUFDOUQ1QyxPQUFPO1FBQ1BsQyxrQkFBa0I7UUFDbEJnRixnQkFBZ0I7UUFDaEJOLG1CQUFtQjtRQUNuQkMsb0JBQW9CO1FBQ3BCckIsV0FBVztRQUNYakQsZUFBZTtRQUNmMEUsUUFBUU0sVUFBVSxZQUFZO1FBQzlCakgsYUFBYTtJQUNmO0FBQ0Y7QUFJRSxDQUNGLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZW1pdW0td29ya3dlYXItc2hvcC8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktY29yZS9idWlsZC9tb2Rlcm4vcXVlcnkuanM/M2E1YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvcXVlcnkudHNcbmltcG9ydCB7XG4gIGVuc3VyZVF1ZXJ5Rm4sXG4gIG5vb3AsXG4gIHJlcGxhY2VEYXRhLFxuICByZXNvbHZlRW5hYmxlZCxcbiAgcmVzb2x2ZVN0YWxlVGltZSxcbiAgc2tpcFRva2VuLFxuICB0aW1lVW50aWxTdGFsZVxufSBmcm9tIFwiLi91dGlscy5qc1wiO1xuaW1wb3J0IHsgbm90aWZ5TWFuYWdlciB9IGZyb20gXCIuL25vdGlmeU1hbmFnZXIuanNcIjtcbmltcG9ydCB7IGNhbkZldGNoLCBjcmVhdGVSZXRyeWVyLCBpc0NhbmNlbGxlZEVycm9yIH0gZnJvbSBcIi4vcmV0cnllci5qc1wiO1xuaW1wb3J0IHsgUmVtb3ZhYmxlIH0gZnJvbSBcIi4vcmVtb3ZhYmxlLmpzXCI7XG52YXIgUXVlcnkgPSBjbGFzcyBleHRlbmRzIFJlbW92YWJsZSB7XG4gICNpbml0aWFsU3RhdGU7XG4gICNyZXZlcnRTdGF0ZTtcbiAgI2NhY2hlO1xuICAjY2xpZW50O1xuICAjcmV0cnllcjtcbiAgI2RlZmF1bHRPcHRpb25zO1xuICAjYWJvcnRTaWduYWxDb25zdW1lZDtcbiAgY29uc3RydWN0b3IoY29uZmlnKSB7XG4gICAgc3VwZXIoKTtcbiAgICB0aGlzLiNhYm9ydFNpZ25hbENvbnN1bWVkID0gZmFsc2U7XG4gICAgdGhpcy4jZGVmYXVsdE9wdGlvbnMgPSBjb25maWcuZGVmYXVsdE9wdGlvbnM7XG4gICAgdGhpcy5zZXRPcHRpb25zKGNvbmZpZy5vcHRpb25zKTtcbiAgICB0aGlzLm9ic2VydmVycyA9IFtdO1xuICAgIHRoaXMuI2NsaWVudCA9IGNvbmZpZy5jbGllbnQ7XG4gICAgdGhpcy4jY2FjaGUgPSB0aGlzLiNjbGllbnQuZ2V0UXVlcnlDYWNoZSgpO1xuICAgIHRoaXMucXVlcnlLZXkgPSBjb25maWcucXVlcnlLZXk7XG4gICAgdGhpcy5xdWVyeUhhc2ggPSBjb25maWcucXVlcnlIYXNoO1xuICAgIHRoaXMuI2luaXRpYWxTdGF0ZSA9IGdldERlZmF1bHRTdGF0ZSh0aGlzLm9wdGlvbnMpO1xuICAgIHRoaXMuc3RhdGUgPSBjb25maWcuc3RhdGUgPz8gdGhpcy4jaW5pdGlhbFN0YXRlO1xuICAgIHRoaXMuc2NoZWR1bGVHYygpO1xuICB9XG4gIGdldCBtZXRhKCkge1xuICAgIHJldHVybiB0aGlzLm9wdGlvbnMubWV0YTtcbiAgfVxuICBnZXQgcHJvbWlzZSgpIHtcbiAgICByZXR1cm4gdGhpcy4jcmV0cnllcj8ucHJvbWlzZTtcbiAgfVxuICBzZXRPcHRpb25zKG9wdGlvbnMpIHtcbiAgICB0aGlzLm9wdGlvbnMgPSB7IC4uLnRoaXMuI2RlZmF1bHRPcHRpb25zLCAuLi5vcHRpb25zIH07XG4gICAgdGhpcy51cGRhdGVHY1RpbWUodGhpcy5vcHRpb25zLmdjVGltZSk7XG4gIH1cbiAgb3B0aW9uYWxSZW1vdmUoKSB7XG4gICAgaWYgKCF0aGlzLm9ic2VydmVycy5sZW5ndGggJiYgdGhpcy5zdGF0ZS5mZXRjaFN0YXR1cyA9PT0gXCJpZGxlXCIpIHtcbiAgICAgIHRoaXMuI2NhY2hlLnJlbW92ZSh0aGlzKTtcbiAgICB9XG4gIH1cbiAgc2V0RGF0YShuZXdEYXRhLCBvcHRpb25zKSB7XG4gICAgY29uc3QgZGF0YSA9IHJlcGxhY2VEYXRhKHRoaXMuc3RhdGUuZGF0YSwgbmV3RGF0YSwgdGhpcy5vcHRpb25zKTtcbiAgICB0aGlzLiNkaXNwYXRjaCh7XG4gICAgICBkYXRhLFxuICAgICAgdHlwZTogXCJzdWNjZXNzXCIsXG4gICAgICBkYXRhVXBkYXRlZEF0OiBvcHRpb25zPy51cGRhdGVkQXQsXG4gICAgICBtYW51YWw6IG9wdGlvbnM/Lm1hbnVhbFxuICAgIH0pO1xuICAgIHJldHVybiBkYXRhO1xuICB9XG4gIHNldFN0YXRlKHN0YXRlLCBzZXRTdGF0ZU9wdGlvbnMpIHtcbiAgICB0aGlzLiNkaXNwYXRjaCh7IHR5cGU6IFwic2V0U3RhdGVcIiwgc3RhdGUsIHNldFN0YXRlT3B0aW9ucyB9KTtcbiAgfVxuICBjYW5jZWwob3B0aW9ucykge1xuICAgIGNvbnN0IHByb21pc2UgPSB0aGlzLiNyZXRyeWVyPy5wcm9taXNlO1xuICAgIHRoaXMuI3JldHJ5ZXI/LmNhbmNlbChvcHRpb25zKTtcbiAgICByZXR1cm4gcHJvbWlzZSA/IHByb21pc2UudGhlbihub29wKS5jYXRjaChub29wKSA6IFByb21pc2UucmVzb2x2ZSgpO1xuICB9XG4gIGRlc3Ryb3koKSB7XG4gICAgc3VwZXIuZGVzdHJveSgpO1xuICAgIHRoaXMuY2FuY2VsKHsgc2lsZW50OiB0cnVlIH0pO1xuICB9XG4gIHJlc2V0KCkge1xuICAgIHRoaXMuZGVzdHJveSgpO1xuICAgIHRoaXMuc2V0U3RhdGUodGhpcy4jaW5pdGlhbFN0YXRlKTtcbiAgfVxuICBpc0FjdGl2ZSgpIHtcbiAgICByZXR1cm4gdGhpcy5vYnNlcnZlcnMuc29tZShcbiAgICAgIChvYnNlcnZlcikgPT4gcmVzb2x2ZUVuYWJsZWQob2JzZXJ2ZXIub3B0aW9ucy5lbmFibGVkLCB0aGlzKSAhPT0gZmFsc2VcbiAgICApO1xuICB9XG4gIGlzRGlzYWJsZWQoKSB7XG4gICAgaWYgKHRoaXMuZ2V0T2JzZXJ2ZXJzQ291bnQoKSA+IDApIHtcbiAgICAgIHJldHVybiAhdGhpcy5pc0FjdGl2ZSgpO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5vcHRpb25zLnF1ZXJ5Rm4gPT09IHNraXBUb2tlbiB8fCB0aGlzLnN0YXRlLmRhdGFVcGRhdGVDb3VudCArIHRoaXMuc3RhdGUuZXJyb3JVcGRhdGVDb3VudCA9PT0gMDtcbiAgfVxuICBpc1N0YXRpYygpIHtcbiAgICBpZiAodGhpcy5nZXRPYnNlcnZlcnNDb3VudCgpID4gMCkge1xuICAgICAgcmV0dXJuIHRoaXMub2JzZXJ2ZXJzLnNvbWUoXG4gICAgICAgIChvYnNlcnZlcikgPT4gcmVzb2x2ZVN0YWxlVGltZShvYnNlcnZlci5vcHRpb25zLnN0YWxlVGltZSwgdGhpcykgPT09IFwic3RhdGljXCJcbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBpc1N0YWxlKCkge1xuICAgIGlmICh0aGlzLmdldE9ic2VydmVyc0NvdW50KCkgPiAwKSB7XG4gICAgICByZXR1cm4gdGhpcy5vYnNlcnZlcnMuc29tZShcbiAgICAgICAgKG9ic2VydmVyKSA9PiBvYnNlcnZlci5nZXRDdXJyZW50UmVzdWx0KCkuaXNTdGFsZVxuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHRoaXMuc3RhdGUuZGF0YSA9PT0gdm9pZCAwIHx8IHRoaXMuc3RhdGUuaXNJbnZhbGlkYXRlZDtcbiAgfVxuICBpc1N0YWxlQnlUaW1lKHN0YWxlVGltZSA9IDApIHtcbiAgICBpZiAodGhpcy5zdGF0ZS5kYXRhID09PSB2b2lkIDApIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBpZiAoc3RhbGVUaW1lID09PSBcInN0YXRpY1wiKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmICh0aGlzLnN0YXRlLmlzSW52YWxpZGF0ZWQpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICByZXR1cm4gIXRpbWVVbnRpbFN0YWxlKHRoaXMuc3RhdGUuZGF0YVVwZGF0ZWRBdCwgc3RhbGVUaW1lKTtcbiAgfVxuICBvbkZvY3VzKCkge1xuICAgIGNvbnN0IG9ic2VydmVyID0gdGhpcy5vYnNlcnZlcnMuZmluZCgoeCkgPT4geC5zaG91bGRGZXRjaE9uV2luZG93Rm9jdXMoKSk7XG4gICAgb2JzZXJ2ZXI/LnJlZmV0Y2goeyBjYW5jZWxSZWZldGNoOiBmYWxzZSB9KTtcbiAgICB0aGlzLiNyZXRyeWVyPy5jb250aW51ZSgpO1xuICB9XG4gIG9uT25saW5lKCkge1xuICAgIGNvbnN0IG9ic2VydmVyID0gdGhpcy5vYnNlcnZlcnMuZmluZCgoeCkgPT4geC5zaG91bGRGZXRjaE9uUmVjb25uZWN0KCkpO1xuICAgIG9ic2VydmVyPy5yZWZldGNoKHsgY2FuY2VsUmVmZXRjaDogZmFsc2UgfSk7XG4gICAgdGhpcy4jcmV0cnllcj8uY29udGludWUoKTtcbiAgfVxuICBhZGRPYnNlcnZlcihvYnNlcnZlcikge1xuICAgIGlmICghdGhpcy5vYnNlcnZlcnMuaW5jbHVkZXMob2JzZXJ2ZXIpKSB7XG4gICAgICB0aGlzLm9ic2VydmVycy5wdXNoKG9ic2VydmVyKTtcbiAgICAgIHRoaXMuY2xlYXJHY1RpbWVvdXQoKTtcbiAgICAgIHRoaXMuI2NhY2hlLm5vdGlmeSh7IHR5cGU6IFwib2JzZXJ2ZXJBZGRlZFwiLCBxdWVyeTogdGhpcywgb2JzZXJ2ZXIgfSk7XG4gICAgfVxuICB9XG4gIHJlbW92ZU9ic2VydmVyKG9ic2VydmVyKSB7XG4gICAgaWYgKHRoaXMub2JzZXJ2ZXJzLmluY2x1ZGVzKG9ic2VydmVyKSkge1xuICAgICAgdGhpcy5vYnNlcnZlcnMgPSB0aGlzLm9ic2VydmVycy5maWx0ZXIoKHgpID0+IHggIT09IG9ic2VydmVyKTtcbiAgICAgIGlmICghdGhpcy5vYnNlcnZlcnMubGVuZ3RoKSB7XG4gICAgICAgIGlmICh0aGlzLiNyZXRyeWVyKSB7XG4gICAgICAgICAgaWYgKHRoaXMuI2Fib3J0U2lnbmFsQ29uc3VtZWQpIHtcbiAgICAgICAgICAgIHRoaXMuI3JldHJ5ZXIuY2FuY2VsKHsgcmV2ZXJ0OiB0cnVlIH0pO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLiNyZXRyeWVyLmNhbmNlbFJldHJ5KCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuc2NoZWR1bGVHYygpO1xuICAgICAgfVxuICAgICAgdGhpcy4jY2FjaGUubm90aWZ5KHsgdHlwZTogXCJvYnNlcnZlclJlbW92ZWRcIiwgcXVlcnk6IHRoaXMsIG9ic2VydmVyIH0pO1xuICAgIH1cbiAgfVxuICBnZXRPYnNlcnZlcnNDb3VudCgpIHtcbiAgICByZXR1cm4gdGhpcy5vYnNlcnZlcnMubGVuZ3RoO1xuICB9XG4gIGludmFsaWRhdGUoKSB7XG4gICAgaWYgKCF0aGlzLnN0YXRlLmlzSW52YWxpZGF0ZWQpIHtcbiAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJpbnZhbGlkYXRlXCIgfSk7XG4gICAgfVxuICB9XG4gIGZldGNoKG9wdGlvbnMsIGZldGNoT3B0aW9ucykge1xuICAgIGlmICh0aGlzLnN0YXRlLmZldGNoU3RhdHVzICE9PSBcImlkbGVcIikge1xuICAgICAgaWYgKHRoaXMuc3RhdGUuZGF0YSAhPT0gdm9pZCAwICYmIGZldGNoT3B0aW9ucz8uY2FuY2VsUmVmZXRjaCkge1xuICAgICAgICB0aGlzLmNhbmNlbCh7IHNpbGVudDogdHJ1ZSB9KTtcbiAgICAgIH0gZWxzZSBpZiAodGhpcy4jcmV0cnllcikge1xuICAgICAgICB0aGlzLiNyZXRyeWVyLmNvbnRpbnVlUmV0cnkoKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuI3JldHJ5ZXIucHJvbWlzZTtcbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKG9wdGlvbnMpIHtcbiAgICAgIHRoaXMuc2V0T3B0aW9ucyhvcHRpb25zKTtcbiAgICB9XG4gICAgaWYgKCF0aGlzLm9wdGlvbnMucXVlcnlGbikge1xuICAgICAgY29uc3Qgb2JzZXJ2ZXIgPSB0aGlzLm9ic2VydmVycy5maW5kKCh4KSA9PiB4Lm9wdGlvbnMucXVlcnlGbik7XG4gICAgICBpZiAob2JzZXJ2ZXIpIHtcbiAgICAgICAgdGhpcy5zZXRPcHRpb25zKG9ic2VydmVyLm9wdGlvbnMpO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgICBpZiAoIUFycmF5LmlzQXJyYXkodGhpcy5vcHRpb25zLnF1ZXJ5S2V5KSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgIGBBcyBvZiB2NCwgcXVlcnlLZXkgbmVlZHMgdG8gYmUgYW4gQXJyYXkuIElmIHlvdSBhcmUgdXNpbmcgYSBzdHJpbmcgbGlrZSAncmVwb0RhdGEnLCBwbGVhc2UgY2hhbmdlIGl0IHRvIGFuIEFycmF5LCBlLmcuIFsncmVwb0RhdGEnXWBcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgYWJvcnRDb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuICAgIGNvbnN0IGFkZFNpZ25hbFByb3BlcnR5ID0gKG9iamVjdCkgPT4ge1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG9iamVjdCwgXCJzaWduYWxcIiwge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6ICgpID0+IHtcbiAgICAgICAgICB0aGlzLiNhYm9ydFNpZ25hbENvbnN1bWVkID0gdHJ1ZTtcbiAgICAgICAgICByZXR1cm4gYWJvcnRDb250cm9sbGVyLnNpZ25hbDtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBmZXRjaEZuID0gKCkgPT4ge1xuICAgICAgY29uc3QgcXVlcnlGbiA9IGVuc3VyZVF1ZXJ5Rm4odGhpcy5vcHRpb25zLCBmZXRjaE9wdGlvbnMpO1xuICAgICAgY29uc3QgY3JlYXRlUXVlcnlGbkNvbnRleHQgPSAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHF1ZXJ5Rm5Db250ZXh0MiA9IHtcbiAgICAgICAgICBjbGllbnQ6IHRoaXMuI2NsaWVudCxcbiAgICAgICAgICBxdWVyeUtleTogdGhpcy5xdWVyeUtleSxcbiAgICAgICAgICBtZXRhOiB0aGlzLm1ldGFcbiAgICAgICAgfTtcbiAgICAgICAgYWRkU2lnbmFsUHJvcGVydHkocXVlcnlGbkNvbnRleHQyKTtcbiAgICAgICAgcmV0dXJuIHF1ZXJ5Rm5Db250ZXh0MjtcbiAgICAgIH07XG4gICAgICBjb25zdCBxdWVyeUZuQ29udGV4dCA9IGNyZWF0ZVF1ZXJ5Rm5Db250ZXh0KCk7XG4gICAgICB0aGlzLiNhYm9ydFNpZ25hbENvbnN1bWVkID0gZmFsc2U7XG4gICAgICBpZiAodGhpcy5vcHRpb25zLnBlcnNpc3Rlcikge1xuICAgICAgICByZXR1cm4gdGhpcy5vcHRpb25zLnBlcnNpc3RlcihcbiAgICAgICAgICBxdWVyeUZuLFxuICAgICAgICAgIHF1ZXJ5Rm5Db250ZXh0LFxuICAgICAgICAgIHRoaXNcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBxdWVyeUZuKHF1ZXJ5Rm5Db250ZXh0KTtcbiAgICB9O1xuICAgIGNvbnN0IGNyZWF0ZUZldGNoQ29udGV4dCA9ICgpID0+IHtcbiAgICAgIGNvbnN0IGNvbnRleHQyID0ge1xuICAgICAgICBmZXRjaE9wdGlvbnMsXG4gICAgICAgIG9wdGlvbnM6IHRoaXMub3B0aW9ucyxcbiAgICAgICAgcXVlcnlLZXk6IHRoaXMucXVlcnlLZXksXG4gICAgICAgIGNsaWVudDogdGhpcy4jY2xpZW50LFxuICAgICAgICBzdGF0ZTogdGhpcy5zdGF0ZSxcbiAgICAgICAgZmV0Y2hGblxuICAgICAgfTtcbiAgICAgIGFkZFNpZ25hbFByb3BlcnR5KGNvbnRleHQyKTtcbiAgICAgIHJldHVybiBjb250ZXh0MjtcbiAgICB9O1xuICAgIGNvbnN0IGNvbnRleHQgPSBjcmVhdGVGZXRjaENvbnRleHQoKTtcbiAgICB0aGlzLm9wdGlvbnMuYmVoYXZpb3I/Lm9uRmV0Y2goY29udGV4dCwgdGhpcyk7XG4gICAgdGhpcy4jcmV2ZXJ0U3RhdGUgPSB0aGlzLnN0YXRlO1xuICAgIGlmICh0aGlzLnN0YXRlLmZldGNoU3RhdHVzID09PSBcImlkbGVcIiB8fCB0aGlzLnN0YXRlLmZldGNoTWV0YSAhPT0gY29udGV4dC5mZXRjaE9wdGlvbnM/Lm1ldGEpIHtcbiAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJmZXRjaFwiLCBtZXRhOiBjb250ZXh0LmZldGNoT3B0aW9ucz8ubWV0YSB9KTtcbiAgICB9XG4gICAgY29uc3Qgb25FcnJvciA9IChlcnJvcikgPT4ge1xuICAgICAgaWYgKCEoaXNDYW5jZWxsZWRFcnJvcihlcnJvcikgJiYgZXJyb3Iuc2lsZW50KSkge1xuICAgICAgICB0aGlzLiNkaXNwYXRjaCh7XG4gICAgICAgICAgdHlwZTogXCJlcnJvclwiLFxuICAgICAgICAgIGVycm9yXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgICAgaWYgKCFpc0NhbmNlbGxlZEVycm9yKGVycm9yKSkge1xuICAgICAgICB0aGlzLiNjYWNoZS5jb25maWcub25FcnJvcj8uKFxuICAgICAgICAgIGVycm9yLFxuICAgICAgICAgIHRoaXNcbiAgICAgICAgKTtcbiAgICAgICAgdGhpcy4jY2FjaGUuY29uZmlnLm9uU2V0dGxlZD8uKFxuICAgICAgICAgIHRoaXMuc3RhdGUuZGF0YSxcbiAgICAgICAgICBlcnJvcixcbiAgICAgICAgICB0aGlzXG4gICAgICAgICk7XG4gICAgICB9XG4gICAgICB0aGlzLnNjaGVkdWxlR2MoKTtcbiAgICB9O1xuICAgIHRoaXMuI3JldHJ5ZXIgPSBjcmVhdGVSZXRyeWVyKHtcbiAgICAgIGluaXRpYWxQcm9taXNlOiBmZXRjaE9wdGlvbnM/LmluaXRpYWxQcm9taXNlLFxuICAgICAgZm46IGNvbnRleHQuZmV0Y2hGbixcbiAgICAgIGFib3J0OiBhYm9ydENvbnRyb2xsZXIuYWJvcnQuYmluZChhYm9ydENvbnRyb2xsZXIpLFxuICAgICAgb25TdWNjZXNzOiAoZGF0YSkgPT4ge1xuICAgICAgICBpZiAoZGF0YSA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgYFF1ZXJ5IGRhdGEgY2Fubm90IGJlIHVuZGVmaW5lZC4gUGxlYXNlIG1ha2Ugc3VyZSB0byByZXR1cm4gYSB2YWx1ZSBvdGhlciB0aGFuIHVuZGVmaW5lZCBmcm9tIHlvdXIgcXVlcnkgZnVuY3Rpb24uIEFmZmVjdGVkIHF1ZXJ5IGtleTogJHt0aGlzLnF1ZXJ5SGFzaH1gXG4gICAgICAgICAgICApO1xuICAgICAgICAgIH1cbiAgICAgICAgICBvbkVycm9yKG5ldyBFcnJvcihgJHt0aGlzLnF1ZXJ5SGFzaH0gZGF0YSBpcyB1bmRlZmluZWRgKSk7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgdGhpcy5zZXREYXRhKGRhdGEpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIG9uRXJyb3IoZXJyb3IpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLiNjYWNoZS5jb25maWcub25TdWNjZXNzPy4oZGF0YSwgdGhpcyk7XG4gICAgICAgIHRoaXMuI2NhY2hlLmNvbmZpZy5vblNldHRsZWQ/LihcbiAgICAgICAgICBkYXRhLFxuICAgICAgICAgIHRoaXMuc3RhdGUuZXJyb3IsXG4gICAgICAgICAgdGhpc1xuICAgICAgICApO1xuICAgICAgICB0aGlzLnNjaGVkdWxlR2MoKTtcbiAgICAgIH0sXG4gICAgICBvbkVycm9yLFxuICAgICAgb25GYWlsOiAoZmFpbHVyZUNvdW50LCBlcnJvcikgPT4ge1xuICAgICAgICB0aGlzLiNkaXNwYXRjaCh7IHR5cGU6IFwiZmFpbGVkXCIsIGZhaWx1cmVDb3VudCwgZXJyb3IgfSk7XG4gICAgICB9LFxuICAgICAgb25QYXVzZTogKCkgPT4ge1xuICAgICAgICB0aGlzLiNkaXNwYXRjaCh7IHR5cGU6IFwicGF1c2VcIiB9KTtcbiAgICAgIH0sXG4gICAgICBvbkNvbnRpbnVlOiAoKSA9PiB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJjb250aW51ZVwiIH0pO1xuICAgICAgfSxcbiAgICAgIHJldHJ5OiBjb250ZXh0Lm9wdGlvbnMucmV0cnksXG4gICAgICByZXRyeURlbGF5OiBjb250ZXh0Lm9wdGlvbnMucmV0cnlEZWxheSxcbiAgICAgIG5ldHdvcmtNb2RlOiBjb250ZXh0Lm9wdGlvbnMubmV0d29ya01vZGUsXG4gICAgICBjYW5SdW46ICgpID0+IHRydWVcbiAgICB9KTtcbiAgICByZXR1cm4gdGhpcy4jcmV0cnllci5zdGFydCgpO1xuICB9XG4gICNkaXNwYXRjaChhY3Rpb24pIHtcbiAgICBjb25zdCByZWR1Y2VyID0gKHN0YXRlKSA9PiB7XG4gICAgICBzd2l0Y2ggKGFjdGlvbi50eXBlKSB7XG4gICAgICAgIGNhc2UgXCJmYWlsZWRcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBmZXRjaEZhaWx1cmVDb3VudDogYWN0aW9uLmZhaWx1cmVDb3VudCxcbiAgICAgICAgICAgIGZldGNoRmFpbHVyZVJlYXNvbjogYWN0aW9uLmVycm9yXG4gICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcInBhdXNlXCI6XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN0YXRlLFxuICAgICAgICAgICAgZmV0Y2hTdGF0dXM6IFwicGF1c2VkXCJcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiY29udGludWVcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBmZXRjaFN0YXR1czogXCJmZXRjaGluZ1wiXG4gICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcImZldGNoXCI6XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN0YXRlLFxuICAgICAgICAgICAgLi4uZmV0Y2hTdGF0ZShzdGF0ZS5kYXRhLCB0aGlzLm9wdGlvbnMpLFxuICAgICAgICAgICAgZmV0Y2hNZXRhOiBhY3Rpb24ubWV0YSA/PyBudWxsXG4gICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcInN1Y2Nlc3NcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBkYXRhOiBhY3Rpb24uZGF0YSxcbiAgICAgICAgICAgIGRhdGFVcGRhdGVDb3VudDogc3RhdGUuZGF0YVVwZGF0ZUNvdW50ICsgMSxcbiAgICAgICAgICAgIGRhdGFVcGRhdGVkQXQ6IGFjdGlvbi5kYXRhVXBkYXRlZEF0ID8/IERhdGUubm93KCksXG4gICAgICAgICAgICBlcnJvcjogbnVsbCxcbiAgICAgICAgICAgIGlzSW52YWxpZGF0ZWQ6IGZhbHNlLFxuICAgICAgICAgICAgc3RhdHVzOiBcInN1Y2Nlc3NcIixcbiAgICAgICAgICAgIC4uLiFhY3Rpb24ubWFudWFsICYmIHtcbiAgICAgICAgICAgICAgZmV0Y2hTdGF0dXM6IFwiaWRsZVwiLFxuICAgICAgICAgICAgICBmZXRjaEZhaWx1cmVDb3VudDogMCxcbiAgICAgICAgICAgICAgZmV0Y2hGYWlsdXJlUmVhc29uOiBudWxsXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcImVycm9yXCI6XG4gICAgICAgICAgY29uc3QgZXJyb3IgPSBhY3Rpb24uZXJyb3I7XG4gICAgICAgICAgaWYgKGlzQ2FuY2VsbGVkRXJyb3IoZXJyb3IpICYmIGVycm9yLnJldmVydCAmJiB0aGlzLiNyZXZlcnRTdGF0ZSkge1xuICAgICAgICAgICAgcmV0dXJuIHsgLi4udGhpcy4jcmV2ZXJ0U3RhdGUsIGZldGNoU3RhdHVzOiBcImlkbGVcIiB9O1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBlcnJvcixcbiAgICAgICAgICAgIGVycm9yVXBkYXRlQ291bnQ6IHN0YXRlLmVycm9yVXBkYXRlQ291bnQgKyAxLFxuICAgICAgICAgICAgZXJyb3JVcGRhdGVkQXQ6IERhdGUubm93KCksXG4gICAgICAgICAgICBmZXRjaEZhaWx1cmVDb3VudDogc3RhdGUuZmV0Y2hGYWlsdXJlQ291bnQgKyAxLFxuICAgICAgICAgICAgZmV0Y2hGYWlsdXJlUmVhc29uOiBlcnJvcixcbiAgICAgICAgICAgIGZldGNoU3RhdHVzOiBcImlkbGVcIixcbiAgICAgICAgICAgIHN0YXR1czogXCJlcnJvclwiXG4gICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcImludmFsaWRhdGVcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBpc0ludmFsaWRhdGVkOiB0cnVlXG4gICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcInNldFN0YXRlXCI6XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN0YXRlLFxuICAgICAgICAgICAgLi4uYWN0aW9uLnN0YXRlXG4gICAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9O1xuICAgIHRoaXMuc3RhdGUgPSByZWR1Y2VyKHRoaXMuc3RhdGUpO1xuICAgIG5vdGlmeU1hbmFnZXIuYmF0Y2goKCkgPT4ge1xuICAgICAgdGhpcy5vYnNlcnZlcnMuZm9yRWFjaCgob2JzZXJ2ZXIpID0+IHtcbiAgICAgICAgb2JzZXJ2ZXIub25RdWVyeVVwZGF0ZSgpO1xuICAgICAgfSk7XG4gICAgICB0aGlzLiNjYWNoZS5ub3RpZnkoeyBxdWVyeTogdGhpcywgdHlwZTogXCJ1cGRhdGVkXCIsIGFjdGlvbiB9KTtcbiAgICB9KTtcbiAgfVxufTtcbmZ1bmN0aW9uIGZldGNoU3RhdGUoZGF0YSwgb3B0aW9ucykge1xuICByZXR1cm4ge1xuICAgIGZldGNoRmFpbHVyZUNvdW50OiAwLFxuICAgIGZldGNoRmFpbHVyZVJlYXNvbjogbnVsbCxcbiAgICBmZXRjaFN0YXR1czogY2FuRmV0Y2gob3B0aW9ucy5uZXR3b3JrTW9kZSkgPyBcImZldGNoaW5nXCIgOiBcInBhdXNlZFwiLFxuICAgIC4uLmRhdGEgPT09IHZvaWQgMCAmJiB7XG4gICAgICBlcnJvcjogbnVsbCxcbiAgICAgIHN0YXR1czogXCJwZW5kaW5nXCJcbiAgICB9XG4gIH07XG59XG5mdW5jdGlvbiBnZXREZWZhdWx0U3RhdGUob3B0aW9ucykge1xuICBjb25zdCBkYXRhID0gdHlwZW9mIG9wdGlvbnMuaW5pdGlhbERhdGEgPT09IFwiZnVuY3Rpb25cIiA/IG9wdGlvbnMuaW5pdGlhbERhdGEoKSA6IG9wdGlvbnMuaW5pdGlhbERhdGE7XG4gIGNvbnN0IGhhc0RhdGEgPSBkYXRhICE9PSB2b2lkIDA7XG4gIGNvbnN0IGluaXRpYWxEYXRhVXBkYXRlZEF0ID0gaGFzRGF0YSA/IHR5cGVvZiBvcHRpb25zLmluaXRpYWxEYXRhVXBkYXRlZEF0ID09PSBcImZ1bmN0aW9uXCIgPyBvcHRpb25zLmluaXRpYWxEYXRhVXBkYXRlZEF0KCkgOiBvcHRpb25zLmluaXRpYWxEYXRhVXBkYXRlZEF0IDogMDtcbiAgcmV0dXJuIHtcbiAgICBkYXRhLFxuICAgIGRhdGFVcGRhdGVDb3VudDogMCxcbiAgICBkYXRhVXBkYXRlZEF0OiBoYXNEYXRhID8gaW5pdGlhbERhdGFVcGRhdGVkQXQgPz8gRGF0ZS5ub3coKSA6IDAsXG4gICAgZXJyb3I6IG51bGwsXG4gICAgZXJyb3JVcGRhdGVDb3VudDogMCxcbiAgICBlcnJvclVwZGF0ZWRBdDogMCxcbiAgICBmZXRjaEZhaWx1cmVDb3VudDogMCxcbiAgICBmZXRjaEZhaWx1cmVSZWFzb246IG51bGwsXG4gICAgZmV0Y2hNZXRhOiBudWxsLFxuICAgIGlzSW52YWxpZGF0ZWQ6IGZhbHNlLFxuICAgIHN0YXR1czogaGFzRGF0YSA/IFwic3VjY2Vzc1wiIDogXCJwZW5kaW5nXCIsXG4gICAgZmV0Y2hTdGF0dXM6IFwiaWRsZVwiXG4gIH07XG59XG5leHBvcnQge1xuICBRdWVyeSxcbiAgZmV0Y2hTdGF0ZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXF1ZXJ5LmpzLm1hcCJdLCJuYW1lcyI6WyJlbnN1cmVRdWVyeUZuIiwibm9vcCIsInJlcGxhY2VEYXRhIiwicmVzb2x2ZUVuYWJsZWQiLCJyZXNvbHZlU3RhbGVUaW1lIiwic2tpcFRva2VuIiwidGltZVVudGlsU3RhbGUiLCJub3RpZnlNYW5hZ2VyIiwiY2FuRmV0Y2giLCJjcmVhdGVSZXRyeWVyIiwiaXNDYW5jZWxsZWRFcnJvciIsIlJlbW92YWJsZSIsIlF1ZXJ5IiwiaW5pdGlhbFN0YXRlIiwicmV2ZXJ0U3RhdGUiLCJjYWNoZSIsImNsaWVudCIsInJldHJ5ZXIiLCJkZWZhdWx0T3B0aW9ucyIsImFib3J0U2lnbmFsQ29uc3VtZWQiLCJjb25zdHJ1Y3RvciIsImNvbmZpZyIsInNldE9wdGlvbnMiLCJvcHRpb25zIiwib2JzZXJ2ZXJzIiwiZ2V0UXVlcnlDYWNoZSIsInF1ZXJ5S2V5IiwicXVlcnlIYXNoIiwiZ2V0RGVmYXVsdFN0YXRlIiwic3RhdGUiLCJzY2hlZHVsZUdjIiwibWV0YSIsInByb21pc2UiLCJ1cGRhdGVHY1RpbWUiLCJnY1RpbWUiLCJvcHRpb25hbFJlbW92ZSIsImxlbmd0aCIsImZldGNoU3RhdHVzIiwicmVtb3ZlIiwic2V0RGF0YSIsIm5ld0RhdGEiLCJkYXRhIiwiZGlzcGF0Y2giLCJ0eXBlIiwiZGF0YVVwZGF0ZWRBdCIsInVwZGF0ZWRBdCIsIm1hbnVhbCIsInNldFN0YXRlIiwic2V0U3RhdGVPcHRpb25zIiwiY2FuY2VsIiwidGhlbiIsImNhdGNoIiwiUHJvbWlzZSIsInJlc29sdmUiLCJkZXN0cm95Iiwic2lsZW50IiwicmVzZXQiLCJpc0FjdGl2ZSIsInNvbWUiLCJvYnNlcnZlciIsImVuYWJsZWQiLCJpc0Rpc2FibGVkIiwiZ2V0T2JzZXJ2ZXJzQ291bnQiLCJxdWVyeUZuIiwiZGF0YVVwZGF0ZUNvdW50IiwiZXJyb3JVcGRhdGVDb3VudCIsImlzU3RhdGljIiwic3RhbGVUaW1lIiwiaXNTdGFsZSIsImdldEN1cnJlbnRSZXN1bHQiLCJpc0ludmFsaWRhdGVkIiwiaXNTdGFsZUJ5VGltZSIsIm9uRm9jdXMiLCJmaW5kIiwieCIsInNob3VsZEZldGNoT25XaW5kb3dGb2N1cyIsInJlZmV0Y2giLCJjYW5jZWxSZWZldGNoIiwiY29udGludWUiLCJvbk9ubGluZSIsInNob3VsZEZldGNoT25SZWNvbm5lY3QiLCJhZGRPYnNlcnZlciIsImluY2x1ZGVzIiwicHVzaCIsImNsZWFyR2NUaW1lb3V0Iiwibm90aWZ5IiwicXVlcnkiLCJyZW1vdmVPYnNlcnZlciIsImZpbHRlciIsInJldmVydCIsImNhbmNlbFJldHJ5IiwiaW52YWxpZGF0ZSIsImZldGNoIiwiZmV0Y2hPcHRpb25zIiwiY29udGludWVSZXRyeSIsInByb2Nlc3MiLCJBcnJheSIsImlzQXJyYXkiLCJjb25zb2xlIiwiZXJyb3IiLCJhYm9ydENvbnRyb2xsZXIiLCJBYm9ydENvbnRyb2xsZXIiLCJhZGRTaWduYWxQcm9wZXJ0eSIsIm9iamVjdCIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZW51bWVyYWJsZSIsImdldCIsInNpZ25hbCIsImZldGNoRm4iLCJjcmVhdGVRdWVyeUZuQ29udGV4dCIsInF1ZXJ5Rm5Db250ZXh0MiIsInF1ZXJ5Rm5Db250ZXh0IiwicGVyc2lzdGVyIiwiY3JlYXRlRmV0Y2hDb250ZXh0IiwiY29udGV4dDIiLCJjb250ZXh0IiwiYmVoYXZpb3IiLCJvbkZldGNoIiwiZmV0Y2hNZXRhIiwib25FcnJvciIsIm9uU2V0dGxlZCIsImluaXRpYWxQcm9taXNlIiwiZm4iLCJhYm9ydCIsImJpbmQiLCJvblN1Y2Nlc3MiLCJFcnJvciIsIm9uRmFpbCIsImZhaWx1cmVDb3VudCIsIm9uUGF1c2UiLCJvbkNvbnRpbnVlIiwicmV0cnkiLCJyZXRyeURlbGF5IiwibmV0d29ya01vZGUiLCJjYW5SdW4iLCJzdGFydCIsImFjdGlvbiIsInJlZHVjZXIiLCJmZXRjaEZhaWx1cmVDb3VudCIsImZldGNoRmFpbHVyZVJlYXNvbiIsImZldGNoU3RhdGUiLCJEYXRlIiwibm93Iiwic3RhdHVzIiwiZXJyb3JVcGRhdGVkQXQiLCJiYXRjaCIsImZvckVhY2giLCJvblF1ZXJ5VXBkYXRlIiwiaW5pdGlhbERhdGEiLCJoYXNEYXRhIiwiaW5pdGlhbERhdGFVcGRhdGVkQXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryCache.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/queryCache.ts\n\n\n\n\nvar QueryCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n    constructor(config = {}){\n        super();\n        this.config = config;\n        this.#queries = /* @__PURE__ */ new Map();\n    }\n    #queries;\n    build(client, options, state) {\n        const queryKey = options.queryKey;\n        const queryHash = options.queryHash ?? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n        let query = this.get(queryHash);\n        if (!query) {\n            query = new _query_js__WEBPACK_IMPORTED_MODULE_2__.Query({\n                client,\n                queryKey,\n                queryHash,\n                options: client.defaultQueryOptions(options),\n                state,\n                defaultOptions: client.getQueryDefaults(queryKey)\n            });\n            this.add(query);\n        }\n        return query;\n    }\n    add(query) {\n        if (!this.#queries.has(query.queryHash)) {\n            this.#queries.set(query.queryHash, query);\n            this.notify({\n                type: \"added\",\n                query\n            });\n        }\n    }\n    remove(query) {\n        const queryInMap = this.#queries.get(query.queryHash);\n        if (queryInMap) {\n            query.destroy();\n            if (queryInMap === query) {\n                this.#queries.delete(query.queryHash);\n            }\n            this.notify({\n                type: \"removed\",\n                query\n            });\n        }\n    }\n    clear() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                this.remove(query);\n            });\n        });\n    }\n    get(queryHash) {\n        return this.#queries.get(queryHash);\n    }\n    getAll() {\n        return [\n            ...this.#queries.values()\n        ];\n    }\n    find(filters) {\n        const defaultedFilters = {\n            exact: true,\n            ...filters\n        };\n        return this.getAll().find((query)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(defaultedFilters, query));\n    }\n    findAll(filters = {}) {\n        const queries = this.getAll();\n        return Object.keys(filters).length > 0 ? queries.filter((query)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : queries;\n    }\n    notify(event) {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.listeners.forEach((listener)=>{\n                listener(event);\n            });\n        });\n    }\n    onFocus() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                query.onFocus();\n            });\n        });\n    }\n    onOnline() {\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(()=>{\n            this.getAll().forEach((query)=>{\n                query.onOnline();\n            });\n        });\n    }\n};\n //# sourceMappingURL=queryCache.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _queryCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/queryClient.ts\n\n\n\n\n\n\n\nvar QueryClient = class {\n    #queryCache;\n    #mutationCache;\n    #defaultOptions;\n    #queryDefaults;\n    #mutationDefaults;\n    #mountCount;\n    #unsubscribeFocus;\n    #unsubscribeOnline;\n    constructor(config = {}){\n        this.#queryCache = config.queryCache || new _queryCache_js__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n        this.#mutationCache = config.mutationCache || new _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n        this.#defaultOptions = config.defaultOptions || {};\n        this.#queryDefaults = /* @__PURE__ */ new Map();\n        this.#mutationDefaults = /* @__PURE__ */ new Map();\n        this.#mountCount = 0;\n    }\n    mount() {\n        this.#mountCount++;\n        if (this.#mountCount !== 1) return;\n        this.#unsubscribeFocus = _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.subscribe(async (focused)=>{\n            if (focused) {\n                await this.resumePausedMutations();\n                this.#queryCache.onFocus();\n            }\n        });\n        this.#unsubscribeOnline = _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.subscribe(async (online)=>{\n            if (online) {\n                await this.resumePausedMutations();\n                this.#queryCache.onOnline();\n            }\n        });\n    }\n    unmount() {\n        this.#mountCount--;\n        if (this.#mountCount !== 0) return;\n        this.#unsubscribeFocus?.();\n        this.#unsubscribeFocus = void 0;\n        this.#unsubscribeOnline?.();\n        this.#unsubscribeOnline = void 0;\n    }\n    isFetching(filters) {\n        return this.#queryCache.findAll({\n            ...filters,\n            fetchStatus: \"fetching\"\n        }).length;\n    }\n    isMutating(filters) {\n        return this.#mutationCache.findAll({\n            ...filters,\n            status: \"pending\"\n        }).length;\n    }\n    /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */ getQueryData(queryKey) {\n        const options = this.defaultQueryOptions({\n            queryKey\n        });\n        return this.#queryCache.get(options.queryHash)?.state.data;\n    }\n    ensureQueryData(options) {\n        const defaultedOptions = this.defaultQueryOptions(options);\n        const query = this.#queryCache.build(this, defaultedOptions);\n        const cachedData = query.state.data;\n        if (cachedData === void 0) {\n            return this.fetchQuery(options);\n        }\n        if (options.revalidateIfStale && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query))) {\n            void this.prefetchQuery(defaultedOptions);\n        }\n        return Promise.resolve(cachedData);\n    }\n    getQueriesData(filters) {\n        return this.#queryCache.findAll(filters).map(({ queryKey, state })=>{\n            const data = state.data;\n            return [\n                queryKey,\n                data\n            ];\n        });\n    }\n    setQueryData(queryKey, updater, options) {\n        const defaultedOptions = this.defaultQueryOptions({\n            queryKey\n        });\n        const query = this.#queryCache.get(defaultedOptions.queryHash);\n        const prevData = query?.state.data;\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.functionalUpdate)(updater, prevData);\n        if (data === void 0) {\n            return void 0;\n        }\n        return this.#queryCache.build(this, defaultedOptions).setData(data, {\n            ...options,\n            manual: true\n        });\n    }\n    setQueriesData(filters, updater, options) {\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).map(({ queryKey })=>[\n                    queryKey,\n                    this.setQueryData(queryKey, updater, options)\n                ]));\n    }\n    getQueryState(queryKey) {\n        const options = this.defaultQueryOptions({\n            queryKey\n        });\n        return this.#queryCache.get(options.queryHash)?.state;\n    }\n    removeQueries(filters) {\n        const queryCache = this.#queryCache;\n        _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            queryCache.findAll(filters).forEach((query)=>{\n                queryCache.remove(query);\n            });\n        });\n    }\n    resetQueries(filters, options) {\n        const queryCache = this.#queryCache;\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            queryCache.findAll(filters).forEach((query)=>{\n                query.reset();\n            });\n            return this.refetchQueries({\n                type: \"active\",\n                ...filters\n            }, options);\n        });\n    }\n    cancelQueries(filters, cancelOptions = {}) {\n        const defaultedCancelOptions = {\n            revert: true,\n            ...cancelOptions\n        };\n        const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).map((query)=>query.cancel(defaultedCancelOptions)));\n        return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    invalidateQueries(filters, options = {}) {\n        return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>{\n            this.#queryCache.findAll(filters).forEach((query)=>{\n                query.invalidate();\n            });\n            if (filters?.refetchType === \"none\") {\n                return Promise.resolve();\n            }\n            return this.refetchQueries({\n                ...filters,\n                type: filters?.refetchType ?? filters?.type ?? \"active\"\n            }, options);\n        });\n    }\n    refetchQueries(filters, options = {}) {\n        const fetchOptions = {\n            ...options,\n            cancelRefetch: options.cancelRefetch ?? true\n        };\n        const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(()=>this.#queryCache.findAll(filters).filter((query)=>!query.isDisabled() && !query.isStatic()).map((query)=>{\n                let promise = query.fetch(void 0, fetchOptions);\n                if (!fetchOptions.throwOnError) {\n                    promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n                }\n                return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n            }));\n        return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    fetchQuery(options) {\n        const defaultedOptions = this.defaultQueryOptions(options);\n        if (defaultedOptions.retry === void 0) {\n            defaultedOptions.retry = false;\n        }\n        const query = this.#queryCache.build(this, defaultedOptions);\n        return query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query)) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n    }\n    prefetchQuery(options) {\n        return this.fetchQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    fetchInfiniteQuery(options) {\n        options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n        return this.fetchQuery(options);\n    }\n    prefetchInfiniteQuery(options) {\n        return this.fetchInfiniteQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }\n    ensureInfiniteQueryData(options) {\n        options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n        return this.ensureQueryData(options);\n    }\n    resumePausedMutations() {\n        if (_onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.isOnline()) {\n            return this.#mutationCache.resumePausedMutations();\n        }\n        return Promise.resolve();\n    }\n    getQueryCache() {\n        return this.#queryCache;\n    }\n    getMutationCache() {\n        return this.#mutationCache;\n    }\n    getDefaultOptions() {\n        return this.#defaultOptions;\n    }\n    setDefaultOptions(options) {\n        this.#defaultOptions = options;\n    }\n    setQueryDefaults(queryKey, options) {\n        this.#queryDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(queryKey), {\n            queryKey,\n            defaultOptions: options\n        });\n    }\n    getQueryDefaults(queryKey) {\n        const defaults = [\n            ...this.#queryDefaults.values()\n        ];\n        const result = {};\n        defaults.forEach((queryDefault)=>{\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(queryKey, queryDefault.queryKey)) {\n                Object.assign(result, queryDefault.defaultOptions);\n            }\n        });\n        return result;\n    }\n    setMutationDefaults(mutationKey, options) {\n        this.#mutationDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(mutationKey), {\n            mutationKey,\n            defaultOptions: options\n        });\n    }\n    getMutationDefaults(mutationKey) {\n        const defaults = [\n            ...this.#mutationDefaults.values()\n        ];\n        const result = {};\n        defaults.forEach((queryDefault)=>{\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(mutationKey, queryDefault.mutationKey)) {\n                Object.assign(result, queryDefault.defaultOptions);\n            }\n        });\n        return result;\n    }\n    defaultQueryOptions(options) {\n        if (options._defaulted) {\n            return options;\n        }\n        const defaultedOptions = {\n            ...this.#defaultOptions.queries,\n            ...this.getQueryDefaults(options.queryKey),\n            ...options,\n            _defaulted: true\n        };\n        if (!defaultedOptions.queryHash) {\n            defaultedOptions.queryHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);\n        }\n        if (defaultedOptions.refetchOnReconnect === void 0) {\n            defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n        }\n        if (defaultedOptions.throwOnError === void 0) {\n            defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n        }\n        if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n            defaultedOptions.networkMode = \"offlineFirst\";\n        }\n        if (defaultedOptions.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_4__.skipToken) {\n            defaultedOptions.enabled = false;\n        }\n        return defaultedOptions;\n    }\n    defaultMutationOptions(options) {\n        if (options?._defaulted) {\n            return options;\n        }\n        return {\n            ...this.#defaultOptions.mutations,\n            ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n            ...options,\n            _defaulted: true\n        };\n    }\n    clear() {\n        this.#queryCache.clear();\n        this.#mutationCache.clear();\n    }\n};\n //# sourceMappingURL=queryClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\nvar Removable = class {\n    #gcTimeout;\n    destroy() {\n        this.clearGcTimeout();\n    }\n    scheduleGc() {\n        this.clearGcTimeout();\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n            this.#gcTimeout = setTimeout(()=>{\n                this.optionalRemove();\n            }, this.gcTime);\n        }\n    }\n    updateGcTime(newGcTime) {\n        this.gcTime = Math.max(this.gcTime || 0, newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3));\n    }\n    clearGcTimeout() {\n        if (this.#gcTimeout) {\n            clearTimeout(this.#gcTimeout);\n            this.#gcTimeout = void 0;\n        }\n    }\n};\n //# sourceMappingURL=removable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n    return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n    return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n    constructor(options){\n        super(\"CancelledError\");\n        this.revert = options?.revert;\n        this.silent = options?.silent;\n    }\n};\nfunction isCancelledError(value) {\n    return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n    let isRetryCancelled = false;\n    let failureCount = 0;\n    let isResolved = false;\n    let continueFn;\n    const thenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n    const cancel = (cancelOptions)=>{\n        if (!isResolved) {\n            reject(new CancelledError(cancelOptions));\n            config.abort?.();\n        }\n    };\n    const cancelRetry = ()=>{\n        isRetryCancelled = true;\n    };\n    const continueRetry = ()=>{\n        isRetryCancelled = false;\n    };\n    const canContinue = ()=>_focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n    const canStart = ()=>canFetch(config.networkMode) && config.canRun();\n    const resolve = (value)=>{\n        if (!isResolved) {\n            isResolved = true;\n            config.onSuccess?.(value);\n            continueFn?.();\n            thenable.resolve(value);\n        }\n    };\n    const reject = (value)=>{\n        if (!isResolved) {\n            isResolved = true;\n            config.onError?.(value);\n            continueFn?.();\n            thenable.reject(value);\n        }\n    };\n    const pause = ()=>{\n        return new Promise((continueResolve)=>{\n            continueFn = (value)=>{\n                if (isResolved || canContinue()) {\n                    continueResolve(value);\n                }\n            };\n            config.onPause?.();\n        }).then(()=>{\n            continueFn = void 0;\n            if (!isResolved) {\n                config.onContinue?.();\n            }\n        });\n    };\n    const run = ()=>{\n        if (isResolved) {\n            return;\n        }\n        let promiseOrValue;\n        const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n        try {\n            promiseOrValue = initialPromise ?? config.fn();\n        } catch (error) {\n            promiseOrValue = Promise.reject(error);\n        }\n        Promise.resolve(promiseOrValue).then(resolve).catch((error)=>{\n            if (isResolved) {\n                return;\n            }\n            const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_3__.isServer ? 0 : 3);\n            const retryDelay = config.retryDelay ?? defaultRetryDelay;\n            const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n            const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n            if (isRetryCancelled || !shouldRetry) {\n                reject(error);\n                return;\n            }\n            failureCount++;\n            config.onFail?.(failureCount, error);\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.sleep)(delay).then(()=>{\n                return canContinue() ? void 0 : pause();\n            }).then(()=>{\n                if (isRetryCancelled) {\n                    reject(error);\n                } else {\n                    run();\n                }\n            });\n        });\n    };\n    return {\n        promise: thenable,\n        cancel,\n        continue: ()=>{\n            continueFn?.();\n            return thenable;\n        },\n        cancelRetry,\n        continueRetry,\n        canStart,\n        start: ()=>{\n            if (canStart()) {\n                run();\n            } else {\n                pause().then(run);\n            }\n            return thenable;\n        }\n    };\n}\n //# sourceMappingURL=retryer.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n    constructor(){\n        this.listeners = /* @__PURE__ */ new Set();\n        this.subscribe = this.subscribe.bind(this);\n    }\n    subscribe(listener) {\n        this.listeners.add(listener);\n        this.onSubscribe();\n        return ()=>{\n            this.listeners.delete(listener);\n            this.onUnsubscribe();\n        };\n    }\n    hasListeners() {\n        return this.listeners.size > 0;\n    }\n    onSubscribe() {}\n    onUnsubscribe() {}\n};\n //# sourceMappingURL=subscribable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsc0JBQXNCO0FBQ3RCLElBQUlBLGVBQWU7SUFDakJDLGFBQWM7UUFDWixJQUFJLENBQUNDLFNBQVMsR0FBRyxhQUFhLEdBQUcsSUFBSUM7UUFDckMsSUFBSSxDQUFDQyxTQUFTLEdBQUcsSUFBSSxDQUFDQSxTQUFTLENBQUNDLElBQUksQ0FBQyxJQUFJO0lBQzNDO0lBQ0FELFVBQVVFLFFBQVEsRUFBRTtRQUNsQixJQUFJLENBQUNKLFNBQVMsQ0FBQ0ssR0FBRyxDQUFDRDtRQUNuQixJQUFJLENBQUNFLFdBQVc7UUFDaEIsT0FBTztZQUNMLElBQUksQ0FBQ04sU0FBUyxDQUFDTyxNQUFNLENBQUNIO1lBQ3RCLElBQUksQ0FBQ0ksYUFBYTtRQUNwQjtJQUNGO0lBQ0FDLGVBQWU7UUFDYixPQUFPLElBQUksQ0FBQ1QsU0FBUyxDQUFDVSxJQUFJLEdBQUc7SUFDL0I7SUFDQUosY0FBYyxDQUNkO0lBQ0FFLGdCQUFnQixDQUNoQjtBQUNGO0FBR0UsQ0FDRix3Q0FBd0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVtaXVtLXdvcmt3ZWFyLXNob3AvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcz9iZWJjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9zdWJzY3JpYmFibGUudHNcbnZhciBTdWJzY3JpYmFibGUgPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMubGlzdGVuZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbiAgICB0aGlzLnN1YnNjcmliZSA9IHRoaXMuc3Vic2NyaWJlLmJpbmQodGhpcyk7XG4gIH1cbiAgc3Vic2NyaWJlKGxpc3RlbmVyKSB7XG4gICAgdGhpcy5saXN0ZW5lcnMuYWRkKGxpc3RlbmVyKTtcbiAgICB0aGlzLm9uU3Vic2NyaWJlKCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHRoaXMubGlzdGVuZXJzLmRlbGV0ZShsaXN0ZW5lcik7XG4gICAgICB0aGlzLm9uVW5zdWJzY3JpYmUoKTtcbiAgICB9O1xuICB9XG4gIGhhc0xpc3RlbmVycygpIHtcbiAgICByZXR1cm4gdGhpcy5saXN0ZW5lcnMuc2l6ZSA+IDA7XG4gIH1cbiAgb25TdWJzY3JpYmUoKSB7XG4gIH1cbiAgb25VbnN1YnNjcmliZSgpIHtcbiAgfVxufTtcbmV4cG9ydCB7XG4gIFN1YnNjcmliYWJsZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN1YnNjcmliYWJsZS5qcy5tYXAiXSwibmFtZXMiOlsiU3Vic2NyaWJhYmxlIiwiY29uc3RydWN0b3IiLCJsaXN0ZW5lcnMiLCJTZXQiLCJzdWJzY3JpYmUiLCJiaW5kIiwibGlzdGVuZXIiLCJhZGQiLCJvblN1YnNjcmliZSIsImRlbGV0ZSIsIm9uVW5zdWJzY3JpYmUiLCJoYXNMaXN0ZW5lcnMiLCJzaXplIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/thenable.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pendingThenable: () => (/* binding */ pendingThenable),\n/* harmony export */   tryResolveSync: () => (/* binding */ tryResolveSync)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/thenable.ts\n\nfunction pendingThenable() {\n    let resolve;\n    let reject;\n    const thenable = new Promise((_resolve, _reject)=>{\n        resolve = _resolve;\n        reject = _reject;\n    });\n    thenable.status = \"pending\";\n    thenable.catch(()=>{});\n    function finalize(data) {\n        Object.assign(thenable, data);\n        delete thenable.resolve;\n        delete thenable.reject;\n    }\n    thenable.resolve = (value)=>{\n        finalize({\n            status: \"fulfilled\",\n            value\n        });\n        resolve(value);\n    };\n    thenable.reject = (reason)=>{\n        finalize({\n            status: \"rejected\",\n            reason\n        });\n        reject(reason);\n    };\n    return thenable;\n}\nfunction tryResolveSync(promise) {\n    let data;\n    promise.then((result)=>{\n        data = result;\n        return result;\n    }, _utils_js__WEBPACK_IMPORTED_MODULE_0__.noop)?.catch(_utils_js__WEBPACK_IMPORTED_MODULE_0__.noop);\n    if (data !== void 0) {\n        return {\n            data\n        };\n    }\n    return void 0;\n}\n //# sourceMappingURL=thenable.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// src/utils.ts\nvar isServer =  true || 0;\nfunction noop() {}\nfunction functionalUpdate(updater, input) {\n    return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n    return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n    return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n    return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n    return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n    const { type = \"all\", exact, fetchStatus, predicate, queryKey, stale } = filters;\n    if (queryKey) {\n        if (exact) {\n            if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n                return false;\n            }\n        } else if (!partialMatchKey(query.queryKey, queryKey)) {\n            return false;\n        }\n    }\n    if (type !== \"all\") {\n        const isActive = query.isActive();\n        if (type === \"active\" && !isActive) {\n            return false;\n        }\n        if (type === \"inactive\" && isActive) {\n            return false;\n        }\n    }\n    if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n        return false;\n    }\n    if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n        return false;\n    }\n    if (predicate && !predicate(query)) {\n        return false;\n    }\n    return true;\n}\nfunction matchMutation(filters, mutation) {\n    const { exact, status, predicate, mutationKey } = filters;\n    if (mutationKey) {\n        if (!mutation.options.mutationKey) {\n            return false;\n        }\n        if (exact) {\n            if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n                return false;\n            }\n        } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n            return false;\n        }\n    }\n    if (status && mutation.state.status !== status) {\n        return false;\n    }\n    if (predicate && !predicate(mutation)) {\n        return false;\n    }\n    return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n    const hashFn = options?.queryKeyHashFn || hashKey;\n    return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n    return JSON.stringify(queryKey, (_, val)=>isPlainObject(val) ? Object.keys(val).sort().reduce((result, key)=>{\n            result[key] = val[key];\n            return result;\n        }, {}) : val);\n}\nfunction partialMatchKey(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (typeof a !== typeof b) {\n        return false;\n    }\n    if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n        return Object.keys(b).every((key)=>partialMatchKey(a[key], b[key]));\n    }\n    return false;\n}\nfunction replaceEqualDeep(a, b) {\n    if (a === b) {\n        return a;\n    }\n    const array = isPlainArray(a) && isPlainArray(b);\n    if (array || isPlainObject(a) && isPlainObject(b)) {\n        const aItems = array ? a : Object.keys(a);\n        const aSize = aItems.length;\n        const bItems = array ? b : Object.keys(b);\n        const bSize = bItems.length;\n        const copy = array ? [] : {};\n        const aItemsSet = new Set(aItems);\n        let equalItems = 0;\n        for(let i = 0; i < bSize; i++){\n            const key = array ? i : bItems[i];\n            if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n                copy[key] = void 0;\n                equalItems++;\n            } else {\n                copy[key] = replaceEqualDeep(a[key], b[key]);\n                if (copy[key] === a[key] && a[key] !== void 0) {\n                    equalItems++;\n                }\n            }\n        }\n        return aSize === bSize && equalItems === aSize ? a : copy;\n    }\n    return b;\n}\nfunction shallowEqualObjects(a, b) {\n    if (!b || Object.keys(a).length !== Object.keys(b).length) {\n        return false;\n    }\n    for(const key in a){\n        if (a[key] !== b[key]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isPlainArray(value) {\n    return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n    if (!hasObjectPrototype(o)) {\n        return false;\n    }\n    const ctor = o.constructor;\n    if (ctor === void 0) {\n        return true;\n    }\n    const prot = ctor.prototype;\n    if (!hasObjectPrototype(prot)) {\n        return false;\n    }\n    if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n        return false;\n    }\n    if (Object.getPrototypeOf(o) !== Object.prototype) {\n        return false;\n    }\n    return true;\n}\nfunction hasObjectPrototype(o) {\n    return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n    return new Promise((resolve)=>{\n        setTimeout(resolve, timeout);\n    });\n}\nfunction replaceData(prevData, data, options) {\n    if (typeof options.structuralSharing === \"function\") {\n        return options.structuralSharing(prevData, data);\n    } else if (options.structuralSharing !== false) {\n        if (true) {\n            try {\n                return replaceEqualDeep(prevData, data);\n            } catch (error) {\n                console.error(`Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`);\n                throw error;\n            }\n        }\n        return replaceEqualDeep(prevData, data);\n    }\n    return data;\n}\nfunction keepPreviousData(previousData) {\n    return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n    const newItems = [\n        ...items,\n        item\n    ];\n    return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n    const newItems = [\n        item,\n        ...items\n    ];\n    return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n    if (true) {\n        if (options.queryFn === skipToken) {\n            console.error(`Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`);\n        }\n    }\n    if (!options.queryFn && fetchOptions?.initialPromise) {\n        return ()=>fetchOptions.initialPromise;\n    }\n    if (!options.queryFn || options.queryFn === skipToken) {\n        return ()=>Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n    }\n    return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n    if (typeof throwOnError === \"function\") {\n        return throwOnError(...params);\n    }\n    return !!throwOnError;\n}\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ })

};
;