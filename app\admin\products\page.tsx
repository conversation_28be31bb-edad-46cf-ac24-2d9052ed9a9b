import { Metadata } from 'next'
import Link from 'next/link'
import { PlusIcon } from '@heroicons/react/24/outline'
import { ProductsTable } from '@/components/admin/products-table'
import { Button } from '@/components/ui/button'

export const metadata: Metadata = {
  title: 'Products | Admin - Elite Workwear',
  description: 'Manage your products',
}

export default function AdminProductsPage() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Products</h1>
          <p className="mt-2 text-gray-600">
            Manage your product catalog and inventory.
          </p>
        </div>
        <Link href="/admin/products/new">
          <Button variant="luxury" size="lg" className="flex items-center space-x-2">
            <PlusIcon className="w-5 h-5" />
            <span>Add Product</span>
          </Button>
        </Link>
      </div>

      <ProductsTable />
    </div>
  )
}
