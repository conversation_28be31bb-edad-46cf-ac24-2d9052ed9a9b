import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Settings | Admin - WorkPro',
  description: 'Store settings',
}

export default function AdminSettingsPage() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold font-display text-gray-900">⚙️ Settings</h1>
        <p className="mt-2 text-gray-600">
          Configure your store settings and preferences.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="admin-card">
          <h3 className="text-lg font-semibold mb-4">🏪 Store Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Store Name</label>
              <input 
                type="text" 
                defaultValue="WorkPro" 
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Store Description</label>
              <textarea 
                rows={3}
                defaultValue="Modern work clothing for every industry"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="admin-card">
          <h3 className="text-lg font-semibold mb-4">📧 Contact Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <input 
                type="email" 
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
              <input 
                type="tel" 
                placeholder="(*************"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="admin-card">
          <h3 className="text-lg font-semibold mb-4">💳 Payment Settings</h3>
          <div className="space-y-4">
            <div className="bg-yellow-50 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                💡 <strong>Coming Soon:</strong> Payment processing with Stripe will be available in the next update.
              </p>
            </div>
          </div>
        </div>

        <div className="admin-card">
          <h3 className="text-lg font-semibold mb-4">🚚 Shipping Settings</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Free Shipping Threshold</label>
              <input 
                type="number" 
                defaultValue="100"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Standard Shipping Cost</label>
              <input 
                type="number" 
                defaultValue="15"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button className="btn-modern">
          💾 Save Settings
        </button>
      </div>
    </div>
  )
}
