import { Metadata } from 'next'
import { HeroSection } from '@/components/sections/hero-section'
import { FeaturedProducts } from '@/components/sections/featured-products'
import { CategoryShowcase } from '@/components/sections/category-showcase'
import { WhyChooseUs } from '@/components/sections/why-choose-us'
import { Testimonials } from '@/components/sections/testimonials'
import { Newsletter } from '@/components/sections/newsletter'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'

export const metadata: Metadata = {
  title: 'WorkPro - Modern Work Clothing & Professional Uniforms',
  description: 'Shop comfortable, durable, and stylish work clothing for every industry. From office wear to industrial uniforms, find quality workwear that fits your job and budget.',
  keywords: [
    'work clothing',
    'professional uniforms',
    'office wear',
    'industrial workwear',
    'comfortable work clothes',
    'durable uniforms',
    'affordable workwear',
    'modern work attire'
  ],
  openGraph: {
    title: 'WorkPro - Modern Work Clothing & Professional Uniforms',
    description: 'Shop comfortable, durable, and stylish work clothing for every industry.',
    images: ['/og-home.jpg'],
  },
}

export default function HomePage() {
  return (
    <div className="min-h-screen bg-luxury-light">
      <Header />
      <main>
        <HeroSection />
        <FeaturedProducts />
        <CategoryShowcase />
        <WhyChooseUs />
        <Testimonials />
        <Newsletter />
      </main>
      <Footer />
    </div>
  )
}
