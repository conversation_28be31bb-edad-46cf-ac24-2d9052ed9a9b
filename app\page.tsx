import { Metadata } from 'next'
import { HeroSection } from '@/components/sections/hero-section'
import { FeaturedProducts } from '@/components/sections/featured-products'
import { CategoryShowcase } from '@/components/sections/category-showcase'
import { WhyChooseUs } from '@/components/sections/why-choose-us'
import { Testimonials } from '@/components/sections/testimonials'
import { Newsletter } from '@/components/sections/newsletter'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'

export const metadata: Metadata = {
  title: 'Elite Workwear - Premium Professional Clothing & Luxury Work Attire',
  description: 'Discover the finest collection of premium workwear and professional clothing. From executive suits to luxury uniforms, our curated selection combines style, comfort, and durability for the modern professional.',
  keywords: [
    'premium workwear',
    'luxury work clothing',
    'professional attire',
    'executive clothing',
    'high-end uniforms',
    'designer workwear',
    'luxury work suits',
    'professional wear online'
  ],
  openGraph: {
    title: 'Elite Workwear - Premium Professional Clothing',
    description: 'Discover the finest collection of premium workwear and professional clothing.',
    images: ['/og-home.jpg'],
  },
}

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <HeroSection />
        <FeaturedProducts />
        <CategoryShowcase />
        <WhyChooseUs />
        <Testimonials />
        <Newsletter />
      </main>
      <Footer />
    </div>
  )
}
