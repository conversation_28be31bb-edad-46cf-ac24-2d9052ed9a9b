import type { Metadata } from 'next'
import { Inter, Playfair_Display } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const playfair = Playfair_Display({ 
  subsets: ['latin'],
  variable: '--font-playfair',
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: 'Elite Workwear - Premium Professional Clothing',
    template: '%s | Elite Workwear'
  },
  description: 'Discover the finest collection of premium workwear and professional clothing. Crafted for excellence, designed for success. Shop luxury work attire that makes a statement.',
  keywords: [
    'premium workwear',
    'luxury work clothing',
    'professional attire',
    'high-end uniforms',
    'executive clothing',
    'designer workwear',
    'premium work suits',
    'luxury professional wear'
  ],
  authors: [{ name: 'Elite Workwear' }],
  creator: 'Elite Workwear',
  publisher: 'Elite Workwear',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://eliteworkwear.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://eliteworkwear.com',
    title: 'Elite Workwear - Premium Professional Clothing',
    description: 'Discover the finest collection of premium workwear and professional clothing. Crafted for excellence, designed for success.',
    siteName: 'Elite Workwear',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Elite Workwear - Premium Professional Clothing',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Elite Workwear - Premium Professional Clothing',
    description: 'Discover the finest collection of premium workwear and professional clothing.',
    images: ['/og-image.jpg'],
    creator: '@eliteworkwear',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${playfair.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#e8b923" />
        <meta name="msapplication-TileColor" content="#e8b923" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <Providers>
          {children}
          <Toaster 
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#e8b923',
                  secondary: '#fff',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  )
}
