import type { <PERSON>ada<PERSON> } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from 'react-hot-toast'
import { StructuredData } from '@/components/seo/structured-data'

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-poppins',
  display: 'swap',
})

const roboto = Roboto({
  subsets: ['latin'],
  weight: ['300', '400', '500', '700', '900'],
  variable: '--font-roboto',
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: 'Elite Work - Ultra Premium Professional Workwear & Luxury Uniforms',
    template: '%s | Elite Work'
  },
  description: 'Experience the pinnacle of professional excellence with our ultra-premium workwear collection. Handcrafted luxury uniforms and elite professional attire for industry leaders who demand perfection.',
  keywords: [
    'luxury workwear',
    'premium professional clothing',
    'elite uniforms',
    'high-end work attire',
    'luxury professional wear',
    'premium work clothing',
    'executive workwear',
    'designer work uniforms',
    'ultra premium workwear',
    'luxury work fashion'
  ],
  authors: [{ name: 'Elite Workwear' }],
  creator: 'Elite Workwear',
  publisher: 'Elite Workwear',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://eliteworkwear.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://eliteworkwear.com',
    title: 'Elite Workwear - Premium Professional Clothing',
    description: 'Discover the finest collection of premium workwear and professional clothing. Crafted for excellence, designed for success.',
    siteName: 'Elite Workwear',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Elite Workwear - Premium Professional Clothing',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Elite Workwear - Premium Professional Clothing',
    description: 'Discover the finest collection of premium workwear and professional clothing.',
    images: ['/og-image.jpg'],
    creator: '@eliteworkwear',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${roboto.variable} ${poppins.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#eab308" />
        <meta name="msapplication-TileColor" content="#eab308" />
      </head>
      <body className={`${roboto.className} antialiased`}>
        <StructuredData type="website" data={{}} />
        <StructuredData type="organization" data={{}} />
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'linear-gradient(135deg, #eab308, #d946ef)',
                color: '#fff',
                fontWeight: 'bold',
                borderRadius: '12px',
                border: '2px solid rgba(255,255,255,0.2)',
                boxShadow: '0 10px 25px rgba(234, 179, 8, 0.3)',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#eab308',
                  secondary: '#fff',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  )
}
