'use client'

import { 
  CurrencyDollarIcon,
  ShoppingBagIcon,
  UsersIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

const stats = [
  {
    name: 'Total Revenue',
    value: '$45,231.89',
    change: '+20.1%',
    changeType: 'positive',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'Orders',
    value: '1,234',
    change: '+15.3%',
    changeType: 'positive',
    icon: ShoppingBagIcon,
  },
  {
    name: 'Customers',
    value: '2,345',
    change: '+8.2%',
    changeType: 'positive',
    icon: UsersIcon,
  },
  {
    name: 'Conversion Rate',
    value: '3.24%',
    change: '-2.1%',
    changeType: 'negative',
    icon: ChartBarIcon,
  },
]

export function DashboardStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat) => (
        <div
          key={stat.name}
          className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow duration-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{stat.name}</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
            </div>
            <div className="w-12 h-12 bg-luxury-50 rounded-lg flex items-center justify-center">
              <stat.icon className="w-6 h-6 text-luxury-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span
              className={`text-sm font-medium ${
                stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {stat.change}
            </span>
            <span className="text-sm text-gray-500 ml-2">from last month</span>
          </div>
        </div>
      ))}
    </div>
  )
}
