import { <PERSON>ada<PERSON> } from 'next'
import { Head<PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'

export const metadata: Metadata = {
  title: 'About Us | WorkPro',
  description: 'Learn about WorkPro and our mission to provide quality work clothing.',
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-display font-bold text-gray-900 mb-4">
              About WorkPro
            </h1>
            <p className="text-xl text-gray-600">
              Your trusted partner for quality work clothing
            </p>
          </div>

          <div className="prose prose-lg mx-auto">
            <div className="bg-blue-50 rounded-2xl p-8 mb-8">
              <h2 className="text-2xl font-bold text-blue-900 mb-4">Our Mission</h2>
              <p className="text-blue-800">
                At WorkPro, we believe that everyone deserves comfortable, durable, and stylish work clothing. 
                Whether you're in an office, on a construction site, or in a healthcare facility, we have the 
                right clothing to help you perform your best.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className="bg-green-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-green-900 mb-3">🌟 Quality First</h3>
                <p className="text-green-800">
                  We source only the highest quality materials and work with trusted manufacturers 
                  to ensure every piece meets our strict standards.
                </p>
              </div>

              <div className="bg-purple-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-purple-900 mb-3">💰 Fair Pricing</h3>
                <p className="text-purple-800">
                  Quality work clothing shouldn't break the bank. We offer competitive prices 
                  without compromising on quality or comfort.
                </p>
              </div>

              <div className="bg-orange-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-orange-900 mb-3">🚚 Fast Shipping</h3>
                <p className="text-orange-800">
                  We know you need your work clothes quickly. That's why we offer fast, 
                  reliable shipping to get your order to you as soon as possible.
                </p>
              </div>

              <div className="bg-indigo-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-indigo-900 mb-3">🤝 Great Service</h3>
                <p className="text-indigo-800">
                  Our customer service team is here to help you find the perfect fit and 
                  answer any questions you might have.
                </p>
              </div>
            </div>

            <div className="bg-gray-50 rounded-2xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Why Choose WorkPro?</h2>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">✓</span>
                  <span>Wide selection of work clothing for every industry</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">✓</span>
                  <span>Comfortable, durable materials that last</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">✓</span>
                  <span>Competitive prices with no hidden fees</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">✓</span>
                  <span>Easy returns and exchanges</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">✓</span>
                  <span>Excellent customer service</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
