'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'

const categories = [
  {
    name: 'Office',
    description: 'Professional office clothing',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    href: '/categories/office-wear'
  },
  {
    name: 'Industrial',
    description: 'Durable work clothing',
    image: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    href: '/categories/industrial'
  },
  {
    name: 'Healthcare',
    description: 'Medical uniforms',
    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    href: '/categories/healthcare'
  }
]

export function CategoryShowcase() {
  return (
    <section className="py-32 bg-gradient-to-br from-white via-luxury-50/30 to-premium-50/30 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360]
          }}
          transition={{ duration: 20, repeat: Infinity }}
          className="absolute top-20 left-20 w-32 h-32 bg-luxury-200/20 rounded-full blur-2xl animate-float"
        />
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 30, 0],
            rotate: [360, 180, 0]
          }}
          transition={{ duration: 25, repeat: Infinity }}
          className="absolute bottom-20 right-20 w-40 h-40 bg-premium-200/20 rounded-full blur-2xl animate-bounce-slow"
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-5xl md:text-7xl font-black text-gray-900 mb-6 animate-fade-in-up">
            {"CATEGORIES".split('').map((letter, index) => (
              <motion.span
                key={index}
                initial={{ opacity: 0, y: 50, rotateX: -90 }}
                whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="inline-block hover:animate-wiggle cursor-default"
                whileHover={{ scale: 1.2, color: "#eab308" }}
              >
                {letter}
              </motion.span>
            ))}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto animate-fade-in-up">
            Choose your work style
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
          {categories.map((category, index) => (
            <motion.div
              key={category.name}
              initial={{ opacity: 0, y: 100, scale: 0.5, rotateY: -90 }}
              whileInView={{ opacity: 1, y: 0, scale: 1, rotateY: 0 }}
              transition={{
                duration: 1,
                delay: index * 0.3,
                type: "spring",
                stiffness: 100
              }}
              viewport={{ once: true }}
              className="group"
              whileHover={{
                scale: 1.05,
                rotateY: 10,
                transition: { duration: 0.3 }
              }}
            >
              <Link href={category.href} className="block">
                <div className="relative aspect-[4/5] rounded-3xl overflow-hidden shadow-2xl group-hover:shadow-luxury-500/30 transition-all duration-500 animate-float hover:animate-rubber-band">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-125 group-hover:rotate-3 transition-all duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent group-hover:from-black/40 transition-all duration-500" />

                  {/* Floating Elements */}
                  <motion.div
                    animate={{
                      y: [-10, 10, -10],
                      x: [-5, 5, -5],
                      rotate: [0, 360]
                    }}
                    transition={{ duration: 6, repeat: Infinity }}
                    className="absolute top-4 right-4 w-4 h-4 bg-luxury-400 rounded-full shadow-lg animate-pulse-fast"
                  />
                  <motion.div
                    animate={{
                      y: [8, -8, 8],
                      scale: [1, 1.3, 1]
                    }}
                    transition={{ duration: 4, repeat: Infinity }}
                    className="absolute bottom-4 left-4 w-3 h-3 bg-premium-400 rounded-full shadow-lg animate-heart-beat"
                  />

                  <div className="absolute bottom-8 left-8 right-8">
                    <motion.h3
                      className="text-3xl font-black text-white mb-2 animate-glow group-hover:animate-tada"
                      whileHover={{ scale: 1.1 }}
                    >
                      {category.name}
                    </motion.h3>
                    <p className="text-gray-200 mb-4 animate-fade-in-up">
                      {category.description}
                    </p>
                    <span className="inline-flex items-center text-luxury-300 font-bold group-hover:text-luxury-200 transition-colors animate-slide-right group-hover:animate-wiggle">
                      Shop Now →
                    </span>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
