"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTranslation: function() { return /* binding */ getTranslation; },\n/* harmony export */   translations: function() { return /* binding */ translations; }\n/* harmony export */ });\nconst translations = {\n    en: {\n        // Navigation\n        home: \"Home\",\n        shop: \"Shop\",\n        categories: \"Categories\",\n        about: \"About\",\n        contact: \"Contact\",\n        // Hero Section\n        heroTitle: \"Elite Professional Workwear\",\n        heroDescription: \"Premium workwear for professionals\",\n        shopNow: \"Shop Now\",\n        learnMore: \"Learn More\",\n        // Products\n        featuredProducts: \"Elite Collection\",\n        featuredDescription: \"Handcrafted premium workwear for the most discerning professionals\",\n        addToCart: \"Add to Cart\",\n        viewDetails: \"View Details\",\n        price: \"Price\",\n        // Categories\n        categoriesTitle: \"Premium Categories\",\n        categoriesDescription: \"Explore our exclusive collections designed for excellence\",\n        officeWear: \"Executive Office Wear\",\n        industrialWear: \"Premium Industrial Gear\",\n        healthcareUniforms: \"Luxury Medical Uniforms\",\n        // Footer\n        followUs: \"Follow Us\",\n        allRightsReserved: \"All rights reserved.\",\n        // Admin\n        dashboard: \"Dashboard\",\n        products: \"Products\",\n        orders: \"Orders\",\n        customers: \"Customers\",\n        settings: \"Settings\",\n        addProduct: \"Add Product\",\n        // Common\n        loading: \"Loading...\",\n        error: \"Error\",\n        success: \"Success\",\n        save: \"Save\",\n        cancel: \"Cancel\",\n        delete: \"Delete\",\n        edit: \"Edit\"\n    },\n    tr: {\n        // Navigation\n        home: \"Ana Sayfa\",\n        shop: \"Mağaza\",\n        categories: \"Kategoriler\",\n        about: \"Hakkımızda\",\n        contact: \"İletişim\",\n        // Hero Section\n        heroTitle: \"Elite Profesyonel İş Kıyafetleri\",\n        heroDescription: \"Profesyoneller i\\xe7in premium iş kıyafetleri\",\n        shopNow: \"Alışveriş Yap\",\n        learnMore: \"Daha Fazla\",\n        // Products\n        featuredProducts: \"Elite Koleksiyon\",\n        featuredDescription: \"En se\\xe7kin profesyoneller i\\xe7in el yapımı premium iş kıyafetleri\",\n        addToCart: \"Sepete Ekle\",\n        viewDetails: \"Detayları G\\xf6r\",\n        price: \"Fiyat\",\n        // Categories\n        categoriesTitle: \"Premium Kategoriler\",\n        categoriesDescription: \"M\\xfckemmellik i\\xe7in tasarlanmış \\xf6zel koleksiyonlarımızı keşfedin\",\n        officeWear: \"Y\\xf6netici Ofis Kıyafetleri\",\n        industrialWear: \"Premium End\\xfcstriyel Ekipman\",\n        healthcareUniforms: \"L\\xfcks Tıbbi \\xdcniformalar\",\n        // Footer\n        followUs: \"Bizi Takip Edin\",\n        allRightsReserved: \"T\\xfcm hakları saklıdır.\",\n        // Admin\n        dashboard: \"Kontrol Paneli\",\n        products: \"\\xdcr\\xfcnler\",\n        orders: \"Siparişler\",\n        customers: \"M\\xfcşteriler\",\n        settings: \"Ayarlar\",\n        addProduct: \"\\xdcr\\xfcn Ekle\",\n        // Common\n        loading: \"Y\\xfckleniyor...\",\n        error: \"Hata\",\n        success: \"Başarılı\",\n        save: \"Kaydet\",\n        cancel: \"İptal\",\n        delete: \"Sil\",\n        edit: \"D\\xfczenle\"\n    },\n    de: {\n        // Navigation\n        home: \"Startseite\",\n        shop: \"Shop\",\n        categories: \"Kategorien\",\n        about: \"\\xdcber uns\",\n        contact: \"Kontakt\",\n        // Hero Section\n        heroTitle: \"Elite Professionelle Arbeitskleidung\",\n        heroSubtitle: \"Luxus • Premium • Exzellenz\",\n        heroDescription: \"Erleben Sie den Gipfel professioneller Kleidung mit unserer exklusiven Kollektion ultra-premium Arbeitskleidung f\\xfcr Branchenf\\xfchrer.\",\n        shopNow: \"Elite Kollektion Shoppen\",\n        learnMore: \"Exzellenz Entdecken\",\n        // Products\n        featuredProducts: \"Elite Kollektion\",\n        featuredDescription: \"Handgefertigte Premium-Arbeitskleidung f\\xfcr anspruchsvollste Profis\",\n        addToCart: \"In den Warenkorb\",\n        viewDetails: \"Details Anzeigen\",\n        price: \"Preis\",\n        // Categories\n        categoriesTitle: \"Premium Kategorien\",\n        categoriesDescription: \"Entdecken Sie unsere exklusiven Kollektionen f\\xfcr Exzellenz\",\n        officeWear: \"Executive B\\xfcrokleidung\",\n        industrialWear: \"Premium Industrieausr\\xfcstung\",\n        healthcareUniforms: \"Luxus Medizinische Uniformen\",\n        // Footer\n        followUs: \"Folgen Sie uns\",\n        allRightsReserved: \"Alle Rechte vorbehalten.\",\n        // Admin\n        dashboard: \"Dashboard\",\n        products: \"Produkte\",\n        orders: \"Bestellungen\",\n        customers: \"Kunden\",\n        settings: \"Einstellungen\",\n        addProduct: \"Produkt Hinzuf\\xfcgen\",\n        // Common\n        loading: \"Laden...\",\n        error: \"Fehler\",\n        success: \"Erfolg\",\n        save: \"Speichern\",\n        cancel: \"Abbrechen\",\n        delete: \"L\\xf6schen\",\n        edit: \"Bearbeiten\"\n    }\n};\nfunction getTranslation(language) {\n    return translations[language] || translations.en;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n.ts\n"));

/***/ })

});