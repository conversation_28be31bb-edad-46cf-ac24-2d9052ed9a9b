import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Orders | Admin - WorkPro',
  description: 'Manage customer orders',
}

export default function AdminOrdersPage() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold font-display text-gray-900">📦 Orders</h1>
        <p className="mt-2 text-gray-600">
          View and manage customer orders. Keep track of what needs to be shipped!
        </p>
      </div>

      <div className="admin-card text-center py-12">
        <div className="text-6xl mb-4">📦</div>
        <h3 className="text-xl font-semibold mb-2">No Orders Yet</h3>
        <p className="text-gray-600 mb-6">
          When customers place orders, they'll appear here for you to manage.
        </p>
        <div className="space-y-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">💡 What you can do here:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• View all customer orders</li>
              <li>• Update order status (processing, shipped, delivered)</li>
              <li>• Print shipping labels</li>
              <li>• Send tracking information to customers</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
