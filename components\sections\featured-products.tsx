'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { formatPrice } from '@/lib/utils'
import { useLanguageStore } from '@/lib/store/language-store'
import { StarIcon, SparklesIcon } from '@heroicons/react/24/solid'

const featuredProducts = [
  {
    id: '1',
    name: 'Work Suit',
    price: 299.99,
    originalPrice: 399.99,
    image: 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: 'NEW',
    rating: 5,
    reviews: 127
  },
  {
    id: '2',
    name: 'Work Blazer',
    price: 199.99,
    originalPrice: 249.99,
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: 'SALE',
    rating: 5,
    reviews: 89
  },
  {
    id: '3',
    name: 'Work Dress',
    price: 149.99,
    originalPrice: 199.99,
    image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: 'HOT',
    rating: 5,
    reviews: 156
  },
  {
    id: '4',
    name: 'Work Shirt',
    price: 89.99,
    originalPrice: 119.99,
    image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: 'TOP',
    rating: 5,
    reviews: 234
  }
]

export function FeaturedProducts() {
  const { translations } = useLanguageStore()

  return (
    <section className="py-32 bg-gradient-to-br from-luxury-50 via-white to-premium-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 50, repeat: Infinity, ease: "linear" }}
          className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-r from-luxury-200/30 to-premium-200/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
          className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-premium-200/30 to-vibrant-200/30 rounded-full blur-3xl"
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex items-center justify-center space-x-4 mb-6"
          >
            <SparklesIcon className="w-10 h-10 text-luxury-500 animate-wiggle" />
            <span className="text-luxury-600 font-bold text-2xl tracking-wider uppercase animate-glow">
              Featured
            </span>
            <SparklesIcon className="w-10 h-10 text-premium-500 animate-wiggle" />
          </motion.div>

          <h2 className="text-5xl md:text-7xl font-black text-elite-900 mb-6 animate-fade-in-up">
            {"WORK CLOTHES".split('').map((letter, index) => (
              <motion.span
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                className="inline-block hover:animate-bounce-slow cursor-default"
                whileHover={{ scale: 1.1, color: "#eab308" }}
              >
                {letter === ' ' ? '\u00A0' : letter}
              </motion.span>
            ))}
          </h2>
          <p className="text-xl text-elite-600 max-w-3xl mx-auto font-medium animate-fade-in-up">
            Quality work clothing for professionals
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
          {featuredProducts.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 100, scale: 0.5, rotateY: -90 }}
              whileInView={{ opacity: 1, y: 0, scale: 1, rotateY: 0 }}
              transition={{
                duration: 1,
                delay: index * 0.2,
                type: "spring",
                stiffness: 100
              }}
              viewport={{ once: true }}
              className="group"
              whileHover={{
                scale: 1.05,
                rotateY: 5,
                transition: { duration: 0.3 }
              }}
            >
              <div className="product-card-elite group relative animate-float hover:animate-rubber-band">
                {/* Animated Badge */}
                <div className="absolute -top-4 -right-4 z-20">
                  <motion.div
                    animate={{
                      rotate: [0, 10, -10, 0],
                      scale: [1, 1.1, 1]
                    }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="bg-gradient-to-r from-luxury-500 to-premium-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-2xl border-2 border-white animate-pulse-fast"
                  >
                    {product.badge}
                  </motion.div>
                </div>

                <div className="relative aspect-[4/5] overflow-hidden rounded-t-3xl">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-125 group-hover:rotate-3 transition-all duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Many Floating Elements */}
                  <motion.div
                    animate={{
                      y: [-8, 8, -8],
                      x: [-3, 3, -3],
                      rotate: [0, 360]
                    }}
                    transition={{ duration: 4, repeat: Infinity }}
                    className="absolute top-4 right-4 w-4 h-4 bg-luxury-400 rounded-full shadow-lg animate-wiggle"
                  />
                  <motion.div
                    animate={{
                      y: [6, -6, 6],
                      x: [2, -2, 2],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{ duration: 3, repeat: Infinity }}
                    className="absolute bottom-4 left-4 w-3 h-3 bg-premium-400 rounded-full shadow-lg animate-heart-beat"
                  />
                  <motion.div
                    animate={{
                      rotate: [0, 180, 360],
                      scale: [0.8, 1.2, 0.8]
                    }}
                    transition={{ duration: 5, repeat: Infinity }}
                    className="absolute top-1/2 left-1/2 w-2 h-2 bg-vibrant-400 rounded-full shadow-lg animate-flash"
                  />
                </div>

                <div className="p-8">
                  {/* Animated Rating */}
                  <div className="flex items-center space-x-1 mb-3">
                    {[...Array(product.rating)].map((_, i) => (
                      <motion.div
                        key={i}
                        initial={{ scale: 0, rotate: -180 }}
                        whileInView={{ scale: 1, rotate: 0 }}
                        transition={{ delay: i * 0.1, duration: 0.5 }}
                      >
                        <StarIcon className="w-5 h-5 text-luxury-500 animate-pulse-fast hover:animate-wiggle" />
                      </motion.div>
                    ))}
                    <span className="text-sm text-elite-600 ml-2 animate-fade-in-left">({product.reviews})</span>
                  </div>

                  <h3 className="text-xl font-bold text-elite-900 mb-3 group-hover:text-luxury-700 transition-colors font-display animate-fade-in-up hover:animate-rubber-band">
                    {product.name}
                  </h3>

                  <div className="flex items-center space-x-3 mb-6">
                    <motion.span
                      className="text-3xl font-black text-luxury-600 animate-glow"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      {formatPrice(product.price)}
                    </motion.span>
                    <span className="text-lg text-gray-500 line-through animate-fade-in-right">
                      {formatPrice(product.originalPrice)}
                    </span>
                  </div>

                  <Link
                    href={`/products/${product.id}`}
                    className="block w-full text-center btn-luxury py-4 text-lg relative overflow-hidden group/btn animate-bounce-slow hover:animate-tada"
                  >
                    <span className="relative z-10 group-hover/btn:animate-wiggle">Add to Cart</span>
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 2 }}
                      className="absolute inset-0 bg-gradient-to-r from-premium-500/20 to-vibrant-500/20 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300 animate-shimmer"
                    />
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Link
            href="/shop"
            className="btn-primary text-lg px-8 py-4"
          >
            View All Products
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
