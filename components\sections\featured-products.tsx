'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { formatPrice } from '@/lib/utils'
import { useLanguageStore } from '@/lib/store/language-store'
import { StarIcon, SparklesIcon } from '@heroicons/react/24/solid'

const featuredProducts = [
  {
    id: '1',
    name: 'Elite Executive Suit',
    price: 2499.99,
    originalPrice: 3299.99,
    image: 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: '👑 ELITE',
    rating: 5,
    reviews: 127
  },
  {
    id: '2',
    name: 'Luxury Work Blazer',
    price: 1899.99,
    originalPrice: 2399.99,
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: '💎 PREMIUM',
    rating: 5,
    reviews: 89
  },
  {
    id: '3',
    name: 'Designer Work Dress',
    price: 1299.99,
    originalPrice: 1699.99,
    image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: '✨ EXCLUSIVE',
    rating: 5,
    reviews: 156
  },
  {
    id: '4',
    name: 'Premium Work Shirt',
    price: 899.99,
    originalPrice: 1199.99,
    image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: '🔥 BESTSELLER',
    rating: 5,
    reviews: 234
  }
]

export function FeaturedProducts() {
  const { translations } = useLanguageStore()

  return (
    <section className="py-32 bg-gradient-to-br from-luxury-50 via-white to-premium-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 50, repeat: Infinity, ease: "linear" }}
          className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-r from-luxury-200/30 to-premium-200/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
          className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-premium-200/30 to-vibrant-200/30 rounded-full blur-3xl"
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex items-center justify-center space-x-4 mb-6"
          >
            <SparklesIcon className="w-8 h-8 text-luxury-500 animate-pulse" />
            <span className="text-luxury-600 font-bold text-xl tracking-wider uppercase">
              {translations.featuredProducts}
            </span>
            <SparklesIcon className="w-8 h-8 text-premium-500 animate-pulse" />
          </motion.div>

          <h2 className="heading-elite mb-6">
            {translations.featuredProducts}
          </h2>
          <p className="text-2xl text-elite-600 max-w-4xl mx-auto font-medium">
            {translations.featuredDescription}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
          {featuredProducts.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="product-card-elite group relative">
                {/* Premium Badge */}
                <div className="absolute -top-4 -right-4 z-20">
                  <motion.div
                    animate={{ rotate: [0, 5, -5, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="bg-gradient-to-r from-luxury-500 to-premium-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-2xl border-2 border-white"
                  >
                    {product.badge}
                  </motion.div>
                </div>

                <div className="relative aspect-[4/5] overflow-hidden rounded-t-3xl">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Floating Elements */}
                  <motion.div
                    animate={{ y: [-5, 5, -5] }}
                    transition={{ duration: 3, repeat: Infinity }}
                    className="absolute top-4 right-4 w-3 h-3 bg-luxury-400 rounded-full shadow-lg opacity-70"
                  />
                </div>

                <div className="p-8">
                  {/* Rating */}
                  <div className="flex items-center space-x-1 mb-3">
                    {[...Array(product.rating)].map((_, i) => (
                      <StarIcon key={i} className="w-5 h-5 text-luxury-500" />
                    ))}
                    <span className="text-sm text-elite-600 ml-2">({product.reviews})</span>
                  </div>

                  <h3 className="text-xl font-bold text-elite-900 mb-3 group-hover:text-luxury-700 transition-colors font-display">
                    {product.name}
                  </h3>

                  <div className="flex items-center space-x-3 mb-6">
                    <span className="text-3xl font-black text-luxury-600">
                      {formatPrice(product.price)}
                    </span>
                    <span className="text-lg text-gray-500 line-through">
                      {formatPrice(product.originalPrice)}
                    </span>
                  </div>

                  <Link
                    href={`/products/${product.id}`}
                    className="block w-full text-center btn-luxury py-4 text-lg relative overflow-hidden group/btn"
                  >
                    <span className="relative z-10">{translations.addToCart}</span>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="absolute inset-0 bg-gradient-to-r from-premium-500/20 to-vibrant-500/20 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"
                    />
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Link
            href="/shop"
            className="btn-primary text-lg px-8 py-4"
          >
            View All Products
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
