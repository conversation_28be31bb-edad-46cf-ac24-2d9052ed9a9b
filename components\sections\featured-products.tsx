'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { formatPrice } from '@/lib/utils'
import { useLanguageStore } from '@/lib/store/language-store'
import { StarIcon, SparklesIcon } from '@heroicons/react/24/solid'

const featuredProducts = [
  {
    id: '1',
    name: 'Work Suit',
    price: 299.99,
    originalPrice: 399.99,
    image: 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: 'NEW',
    rating: 5,
    reviews: 127
  },
  {
    id: '2',
    name: 'Work Blazer',
    price: 199.99,
    originalPrice: 249.99,
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: 'SALE',
    rating: 5,
    reviews: 89
  },
  {
    id: '3',
    name: 'Work Dress',
    price: 149.99,
    originalPrice: 199.99,
    image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: 'HOT',
    rating: 5,
    reviews: 156
  },
  {
    id: '4',
    name: 'Work Shirt',
    price: 89.99,
    originalPrice: 119.99,
    image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
    badge: 'TOP',
    rating: 5,
    reviews: 234
  }
]

export function FeaturedProducts() {
  const { translations } = useLanguageStore()

  return (
    <section className="py-20 bg-white relative">
      {/* Subtle Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{ opacity: [0.1, 0.3, 0.1] }}
          transition={{ duration: 8, repeat: Infinity }}
          className="absolute top-20 right-20 w-32 h-32 bg-classic-200/20 rounded-full blur-2xl"
        />
        <motion.div
          animate={{ opacity: [0.2, 0.4, 0.2] }}
          transition={{ duration: 10, repeat: Infinity }}
          className="absolute bottom-20 left-20 w-40 h-40 bg-professional-200/20 rounded-full blur-2xl"
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-6"
          >
            <span className="text-classic-600 font-semibold text-lg tracking-wide uppercase">
              Featured Collection
            </span>
          </motion.div>

          <h2 className="heading-classic mb-6 animate-classic-fade">
            WORK CLOTHES
          </h2>
          <p className="text-lg text-classic-600 max-w-2xl mx-auto">
            Quality work clothing for professionals
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {featuredProducts.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.6,
                delay: index * 0.1
              }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="product-card-professional group relative animate-classic-hover">
                {/* Classic Badge */}
                <div className="absolute -top-2 -right-2 z-20">
                  <div className="bg-classic-600 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
                    {product.badge}
                  </div>
                </div>

                <div className="relative aspect-[4/5] overflow-hidden rounded-t-xl">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                </div>

                <div className="p-6">
                  {/* Classic Rating */}
                  <div className="flex items-center space-x-1 mb-3">
                    {[...Array(product.rating)].map((_, i) => (
                      <StarIcon key={i} className="w-4 h-4 text-classic-500" />
                    ))}
                    <span className="text-sm text-classic-600 ml-2">({product.reviews})</span>
                  </div>

                  <h3 className="text-lg font-semibold text-classic-900 mb-3 group-hover:text-classic-700 transition-colors">
                    {product.name}
                  </h3>

                  <div className="flex items-center space-x-2 mb-4">
                    <span className="text-2xl font-bold text-classic-600">
                      {formatPrice(product.price)}
                    </span>
                    <span className="text-base text-gray-500 line-through">
                      {formatPrice(product.originalPrice)}
                    </span>
                  </div>

                  <Link
                    href={`/products/${product.id}`}
                    className="block w-full text-center btn-classic py-3 text-base animate-classic-hover"
                  >
                    Add to Cart
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Link
            href="/shop"
            className="btn-primary text-lg px-8 py-4"
          >
            View All Products
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
