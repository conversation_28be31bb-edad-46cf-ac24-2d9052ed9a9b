@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap');

:root {
  --font-poppins: 'Poppins', sans-serif;
  --font-roboto: 'Roboto', sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Ultra Premium Elite Theme */
  .luxury-gradient {
    @apply bg-gradient-to-br from-luxury-400 via-luxury-500 to-luxury-600;
  }

  .elite-gradient {
    @apply bg-gradient-to-r from-elite-900 via-elite-800 to-elite-700;
  }

  .premium-gradient {
    @apply bg-gradient-to-br from-premium-500 via-premium-600 to-vibrant-500;
  }

  .luxury-text-gradient {
    @apply bg-gradient-to-r from-luxury-600 via-premium-600 to-vibrant-600 bg-clip-text text-transparent;
  }

  .elite-shadow {
    box-shadow: 0 25px 50px -12px rgba(234, 179, 8, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .premium-shadow {
    box-shadow: 0 35px 60px -12px rgba(217, 70, 239, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .glass-luxury {
    @apply backdrop-blur-2xl bg-gradient-to-br from-white/20 to-white/5 border border-white/30 rounded-3xl;
  }

  .btn-luxury {
    @apply bg-gradient-to-r from-luxury-500 to-luxury-600 hover:from-luxury-600 hover:to-luxury-700 text-white font-bold py-4 px-10 rounded-2xl transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-luxury-500/50 border border-luxury-400/50;
  }

  .btn-premium {
    @apply bg-gradient-to-r from-premium-500 to-vibrant-500 hover:from-premium-600 hover:to-vibrant-600 text-white font-bold py-4 px-10 rounded-2xl transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-premium-500/50;
  }

  .btn-elite {
    @apply bg-gradient-to-r from-elite-800 to-elite-900 hover:from-elite-900 hover:to-elite-950 text-white font-bold py-4 px-10 rounded-2xl transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-elite-800/50 border border-elite-600/50;
  }

  .card-luxury {
    @apply bg-gradient-to-br from-white to-luxury-50/30 rounded-3xl shadow-2xl hover:shadow-luxury-500/20 transition-all duration-700 transform hover:-translate-y-3 hover:scale-105 border border-luxury-200/50 backdrop-blur-sm;
  }

  .product-card-elite {
    @apply bg-gradient-to-br from-white via-luxury-50/20 to-premium-50/20 rounded-3xl shadow-2xl hover:shadow-luxury-400/30 transition-all duration-700 transform hover:-translate-y-4 hover:scale-105 overflow-hidden border-2 border-luxury-200/50 backdrop-blur-lg;
  }

  .admin-card-premium {
    @apply bg-gradient-to-br from-white to-luxury-50/50 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 border-2 border-luxury-200/50 p-8 backdrop-blur-sm;
  }

  .text-luxury-shadow {
    text-shadow: 0 8px 16px rgba(234, 179, 8, 0.3), 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .text-premium-shadow {
    text-shadow: 0 8px 16px rgba(217, 70, 239, 0.3), 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    animation: shimmer 3s linear infinite;
  }

  .animate-bounce-slow {
    animation: bounce-slow 3s ease-in-out infinite;
  }

  .animate-wiggle {
    animation: wiggle 1s ease-in-out infinite;
  }

  .animate-pulse-fast {
    animation: pulse-fast 1s ease-in-out infinite;
  }

  .animate-rotate-slow {
    animation: rotate-slow 10s linear infinite;
  }

  .animate-slide-up {
    animation: slide-up 0.8s ease-out;
  }

  .animate-slide-down {
    animation: slide-down 0.8s ease-out;
  }

  .animate-slide-left {
    animation: slide-left 0.8s ease-out;
  }

  .animate-slide-right {
    animation: slide-right 0.8s ease-out;
  }

  .animate-zoom-in {
    animation: zoom-in 0.6s ease-out;
  }

  .animate-zoom-out {
    animation: zoom-out 0.6s ease-out;
  }

  .animate-fade-in {
    animation: fade-in 1s ease-out;
  }

  .animate-fade-in-up {
    animation: fade-in-up 1s ease-out;
  }

  .animate-fade-in-down {
    animation: fade-in-down 1s ease-out;
  }

  .animate-fade-in-left {
    animation: fade-in-left 1s ease-out;
  }

  .animate-fade-in-right {
    animation: fade-in-right 1s ease-out;
  }

  .animate-scale-up {
    animation: scale-up 0.5s ease-out;
  }

  .animate-scale-down {
    animation: scale-down 0.5s ease-out;
  }

  .animate-flip {
    animation: flip 1s ease-in-out;
  }

  .animate-flip-horizontal {
    animation: flip-horizontal 1s ease-in-out;
  }

  .animate-shake {
    animation: shake 0.5s ease-in-out;
  }

  .animate-rubber-band {
    animation: rubber-band 1s ease-in-out;
  }

  .animate-swing {
    animation: swing 1s ease-in-out;
  }

  .animate-tada {
    animation: tada 1s ease-in-out;
  }

  .animate-wobble {
    animation: wobble 1s ease-in-out;
  }

  .animate-jello {
    animation: jello 1s ease-in-out;
  }

  .animate-heart-beat {
    animation: heart-beat 1.5s ease-in-out infinite;
  }

  .animate-flash {
    animation: flash 2s ease-in-out infinite;
  }

  .animate-head-shake {
    animation: head-shake 1s ease-in-out;
  }
}

/* Luxury scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  @apply bg-gradient-to-b from-luxury-50 to-luxury-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gradient-to-b from-luxury-400 to-luxury-600 rounded-full shadow-lg;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gradient-to-b from-luxury-500 to-luxury-700;
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Smooth page transitions */
.page-transition {
  @apply transition-all duration-500 ease-in-out;
}

/* Ultra Premium Typography */
.heading-luxury {
  @apply font-black text-5xl md:text-7xl lg:text-8xl luxury-text-gradient text-luxury-shadow animate-glow;
  font-family: var(--font-poppins);
  letter-spacing: -0.02em;
}

.heading-elite {
  @apply font-bold text-4xl md:text-6xl lg:text-7xl text-elite-900 text-premium-shadow;
  font-family: var(--font-poppins);
}

.subheading-luxury {
  @apply font-bold text-3xl md:text-4xl lg:text-5xl text-elite-800;
  font-family: var(--font-poppins);
}

.body-luxury {
  @apply text-elite-700 leading-relaxed text-lg;
  font-family: var(--font-roboto);
}

.text-luxury-primary {
  @apply text-luxury-600;
}

.text-premium-primary {
  @apply text-premium-600;
}

.text-elite-primary {
  @apply text-elite-800;
}

.bg-luxury-light {
  @apply bg-gradient-to-br from-luxury-50 via-white to-premium-50;
}

/* Premium animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  from { text-shadow: 0 8px 16px rgba(234, 179, 8, 0.3), 0 4px 8px rgba(0, 0, 0, 0.1); }
  to { text-shadow: 0 12px 24px rgba(234, 179, 8, 0.5), 0 6px 12px rgba(217, 70, 239, 0.2); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes bounce-slow {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-15px); }
  70% { transform: translateY(-7px); }
  90% { transform: translateY(-3px); }
}

@keyframes wiggle {
  0%, 7% { transform: rotateZ(0); }
  15% { transform: rotateZ(-15deg); }
  20% { transform: rotateZ(10deg); }
  25% { transform: rotateZ(-10deg); }
  30% { transform: rotateZ(6deg); }
  35% { transform: rotateZ(-4deg); }
  40%, 100% { transform: rotateZ(0); }
}

@keyframes pulse-fast {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes rotate-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slide-up {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slide-down {
  from { transform: translateY(-100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slide-left {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-right {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes zoom-in {
  from { transform: scale(0); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes zoom-out {
  from { transform: scale(1.2); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fade-in-up {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fade-in-down {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fade-in-left {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes fade-in-right {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes scale-up {
  from { transform: scale(0.8); }
  to { transform: scale(1); }
}

@keyframes scale-down {
  from { transform: scale(1.2); }
  to { transform: scale(1); }
}

@keyframes flip {
  from { transform: perspective(400px) rotateY(-90deg); }
  to { transform: perspective(400px) rotateY(0deg); }
}

@keyframes flip-horizontal {
  from { transform: perspective(400px) rotateX(-90deg); }
  to { transform: perspective(400px) rotateX(0deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
  20%, 40%, 60%, 80% { transform: translateX(10px); }
}

@keyframes rubber-band {
  from { transform: scale3d(1, 1, 1); }
  30% { transform: scale3d(1.25, 0.75, 1); }
  40% { transform: scale3d(0.75, 1.25, 1); }
  50% { transform: scale3d(1.15, 0.85, 1); }
  65% { transform: scale3d(0.95, 1.05, 1); }
  75% { transform: scale3d(1.05, 0.95, 1); }
  to { transform: scale3d(1, 1, 1); }
}

@keyframes swing {
  20% { transform: rotate3d(0, 0, 1, 15deg); }
  40% { transform: rotate3d(0, 0, 1, -10deg); }
  60% { transform: rotate3d(0, 0, 1, 5deg); }
  80% { transform: rotate3d(0, 0, 1, -5deg); }
  to { transform: rotate3d(0, 0, 1, 0deg); }
}

@keyframes tada {
  from { transform: scale3d(1, 1, 1); }
  10%, 20% { transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg); }
  30%, 50%, 70%, 90% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg); }
  40%, 60%, 80% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg); }
  to { transform: scale3d(1, 1, 1); }
}

@keyframes wobble {
  from { transform: translate3d(0, 0, 0); }
  15% { transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg); }
  30% { transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg); }
  45% { transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg); }
  60% { transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg); }
  75% { transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg); }
  to { transform: translate3d(0, 0, 0); }
}

@keyframes jello {
  from, 11.1%, to { transform: translate3d(0, 0, 0); }
  22.2% { transform: skewX(-12.5deg) skewY(-12.5deg); }
  33.3% { transform: skewX(6.25deg) skewY(6.25deg); }
  44.4% { transform: skewX(-3.125deg) skewY(-3.125deg); }
  55.5% { transform: skewX(1.5625deg) skewY(1.5625deg); }
  66.6% { transform: skewX(-0.78125deg) skewY(-0.78125deg); }
  77.7% { transform: skewX(0.390625deg) skewY(0.390625deg); }
  88.8% { transform: skewX(-0.1953125deg) skewY(-0.1953125deg); }
}

@keyframes heart-beat {
  0% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}

@keyframes flash {
  from, 50%, to { opacity: 1; }
  25%, 75% { opacity: 0; }
}

@keyframes head-shake {
  0% { transform: translateX(0); }
  6.5% { transform: translateX(-6px) rotateY(-9deg); }
  18.5% { transform: translateX(5px) rotateY(7deg); }
  31.5% { transform: translateX(-3px) rotateY(-5deg); }
  43.5% { transform: translateX(2px) rotateY(3deg); }
  50% { transform: translateX(0); }
}
