@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap');

:root {
  --font-poppins: 'Poppins', sans-serif;
  --font-roboto: 'Roboto', sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Classic Professional Theme */
  .classic-gradient {
    @apply bg-gradient-to-br from-classic-800 via-classic-900 to-professional-900;
  }

  .professional-gradient {
    @apply bg-gradient-to-r from-professional-800 via-professional-900 to-classic-900;
  }

  .classic-text-gradient {
    @apply bg-gradient-to-r from-classic-600 to-professional-700 bg-clip-text text-transparent;
  }

  .classic-shadow {
    box-shadow: 0 20px 40px -12px rgba(15, 23, 42, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .professional-shadow {
    box-shadow: 0 25px 50px -12px rgba(39, 39, 42, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .glass-classic {
    @apply backdrop-blur-xl bg-white/10 border border-white/20 rounded-xl;
  }

  .btn-classic {
    @apply bg-gradient-to-r from-classic-700 to-classic-800 hover:from-classic-800 hover:to-classic-900 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl border border-classic-600/50;
  }

  .btn-professional {
    @apply bg-gradient-to-r from-professional-700 to-professional-800 hover:from-professional-800 hover:to-professional-900 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .btn-outline-classic {
    @apply bg-transparent border-2 border-classic-600 text-classic-700 hover:bg-classic-50 font-semibold py-3 px-8 rounded-lg transition-all duration-300;
  }

  .card-classic {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200;
  }

  .product-card-professional {
    @apply bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden border border-gray-100;
  }

  .admin-card-classic {
    @apply bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200 p-6;
  }

  .text-classic-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-professional-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* Classic Cool Animations */
  .animate-classic-fade {
    animation: classic-fade 0.8s ease-out;
  }

  .animate-classic-slide {
    animation: classic-slide 0.6s ease-out;
  }

  .animate-classic-scale {
    animation: classic-scale 0.5s ease-out;
  }

  .animate-classic-rotate {
    animation: classic-rotate 0.7s ease-out;
  }

  .animate-typewriter {
    animation: typewriter 2s steps(20) 1s both;
  }

  .animate-underline {
    animation: underline 0.8s ease-out;
  }

  .animate-classic-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-classic-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  .animate-professional-entrance {
    animation: professional-entrance 1s ease-out;
  }

  .animate-smooth-bounce {
    animation: smooth-bounce 2s ease-in-out infinite;
  }

  .animate-elegant-pulse {
    animation: elegant-pulse 3s ease-in-out infinite;
  }
}

/* Classic Professional scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-classic-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-classic-500;
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Smooth page transitions */
.page-transition {
  @apply transition-all duration-500 ease-in-out;
}

/* Classic Professional Typography */
.heading-classic {
  @apply font-bold text-4xl md:text-5xl lg:text-6xl text-classic-900 text-classic-shadow;
  font-family: var(--font-poppins);
  letter-spacing: -0.01em;
}

.heading-professional {
  @apply font-semibold text-3xl md:text-4xl lg:text-5xl text-professional-800;
  font-family: var(--font-poppins);
}

.subheading-classic {
  @apply font-semibold text-2xl md:text-3xl text-classic-800;
  font-family: var(--font-poppins);
}

.body-classic {
  @apply text-classic-700 leading-relaxed text-base;
  font-family: var(--font-roboto);
}

.text-classic-primary {
  @apply text-classic-600;
}

.text-professional-primary {
  @apply text-professional-600;
}

.bg-classic-light {
  @apply bg-gradient-to-br from-gray-50 via-white to-classic-50;
}

/* Classic Cool Animations */
@keyframes classic-fade {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes classic-slide {
  from { transform: translateX(-30px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes classic-scale {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes classic-rotate {
  from { transform: rotate(-5deg) scale(0.9); opacity: 0; }
  to { transform: rotate(0deg) scale(1); opacity: 1; }
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes underline {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes professional-entrance {
  0% { opacity: 0; transform: translateY(30px) scale(0.95); }
  100% { opacity: 1; transform: translateY(0) scale(1); }
}

@keyframes smooth-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes elegant-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}


