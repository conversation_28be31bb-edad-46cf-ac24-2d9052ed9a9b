@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap');

:root {
  --font-poppins: 'Poppins', sans-serif;
  --font-roboto: 'Roboto', sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Ultra Premium Elite Theme */
  .luxury-gradient {
    @apply bg-gradient-to-br from-luxury-400 via-luxury-500 to-luxury-600;
  }

  .elite-gradient {
    @apply bg-gradient-to-r from-elite-900 via-elite-800 to-elite-700;
  }

  .premium-gradient {
    @apply bg-gradient-to-br from-premium-500 via-premium-600 to-vibrant-500;
  }

  .luxury-text-gradient {
    @apply bg-gradient-to-r from-luxury-600 via-premium-600 to-vibrant-600 bg-clip-text text-transparent;
  }

  .elite-shadow {
    box-shadow: 0 25px 50px -12px rgba(234, 179, 8, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .premium-shadow {
    box-shadow: 0 35px 60px -12px rgba(217, 70, 239, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .glass-luxury {
    @apply backdrop-blur-2xl bg-gradient-to-br from-white/20 to-white/5 border border-white/30 rounded-3xl;
  }

  .btn-luxury {
    @apply bg-gradient-to-r from-luxury-500 to-luxury-600 hover:from-luxury-600 hover:to-luxury-700 text-white font-bold py-4 px-10 rounded-2xl transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-luxury-500/50 border border-luxury-400/50;
  }

  .btn-premium {
    @apply bg-gradient-to-r from-premium-500 to-vibrant-500 hover:from-premium-600 hover:to-vibrant-600 text-white font-bold py-4 px-10 rounded-2xl transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-premium-500/50;
  }

  .btn-elite {
    @apply bg-gradient-to-r from-elite-800 to-elite-900 hover:from-elite-900 hover:to-elite-950 text-white font-bold py-4 px-10 rounded-2xl transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-elite-800/50 border border-elite-600/50;
  }

  .card-luxury {
    @apply bg-gradient-to-br from-white to-luxury-50/30 rounded-3xl shadow-2xl hover:shadow-luxury-500/20 transition-all duration-700 transform hover:-translate-y-3 hover:scale-105 border border-luxury-200/50 backdrop-blur-sm;
  }

  .product-card-elite {
    @apply bg-gradient-to-br from-white via-luxury-50/20 to-premium-50/20 rounded-3xl shadow-2xl hover:shadow-luxury-400/30 transition-all duration-700 transform hover:-translate-y-4 hover:scale-105 overflow-hidden border-2 border-luxury-200/50 backdrop-blur-lg;
  }

  .admin-card-premium {
    @apply bg-gradient-to-br from-white to-luxury-50/50 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 border-2 border-luxury-200/50 p-8 backdrop-blur-sm;
  }

  .text-luxury-shadow {
    text-shadow: 0 8px 16px rgba(234, 179, 8, 0.3), 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .text-premium-shadow {
    text-shadow: 0 8px 16px rgba(217, 70, 239, 0.3), 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    animation: shimmer 3s linear infinite;
  }
}

/* Luxury scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  @apply bg-gradient-to-b from-luxury-50 to-luxury-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gradient-to-b from-luxury-400 to-luxury-600 rounded-full shadow-lg;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gradient-to-b from-luxury-500 to-luxury-700;
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Smooth page transitions */
.page-transition {
  @apply transition-all duration-500 ease-in-out;
}

/* Ultra Premium Typography */
.heading-luxury {
  @apply font-black text-5xl md:text-7xl lg:text-8xl luxury-text-gradient text-luxury-shadow animate-glow;
  font-family: var(--font-poppins);
  letter-spacing: -0.02em;
}

.heading-elite {
  @apply font-bold text-4xl md:text-6xl lg:text-7xl text-elite-900 text-premium-shadow;
  font-family: var(--font-poppins);
}

.subheading-luxury {
  @apply font-bold text-3xl md:text-4xl lg:text-5xl text-elite-800;
  font-family: var(--font-poppins);
}

.body-luxury {
  @apply text-elite-700 leading-relaxed text-lg;
  font-family: var(--font-roboto);
}

.text-luxury-primary {
  @apply text-luxury-600;
}

.text-premium-primary {
  @apply text-premium-600;
}

.text-elite-primary {
  @apply text-elite-800;
}

.bg-luxury-light {
  @apply bg-gradient-to-br from-luxury-50 via-white to-premium-50;
}

/* Premium animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  from { text-shadow: 0 8px 16px rgba(234, 179, 8, 0.3), 0 4px 8px rgba(0, 0, 0, 0.1); }
  to { text-shadow: 0 12px 24px rgba(234, 179, 8, 0.5), 0 6px 12px rgba(217, 70, 239, 0.2); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}
