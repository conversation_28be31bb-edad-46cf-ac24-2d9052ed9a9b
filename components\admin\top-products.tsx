'use client'

import { formatPrice } from '@/lib/utils'

const topProducts = [
  { name: 'Executive Premium Suit', sales: 45, revenue: 40455 },
  { name: 'Luxury Work Shirt', sales: 32, revenue: 4799.68 },
  { name: 'Professional Blazer', sales: 28, revenue: 8399.72 },
  { name: 'Designer Work Dress', sales: 22, revenue: 5499.78 },
]

export function TopProducts() {
  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Products</h3>
      <div className="space-y-4">
        {topProducts.map((product, index) => (
          <div key={product.name} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
              <div>
                <p className="text-sm font-medium text-gray-900">{product.name}</p>
                <p className="text-xs text-gray-500">{product.sales} sales</p>
              </div>
            </div>
            <p className="text-sm font-semibold text-gray-900">
              {formatPrice(product.revenue)}
            </p>
          </div>
        ))}
      </div>
    </div>
  )
}
