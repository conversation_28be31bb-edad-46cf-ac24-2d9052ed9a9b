export type Language = 'en' | 'tr' | 'de'

export interface Translations {
  // Navigation
  home: string
  shop: string
  categories: string
  about: string
  contact: string
  
  // Hero Section
  heroTitle: string
  heroDescription: string
  shopNow: string
  learnMore: string
  
  // Products
  featuredProducts: string
  featuredDescription: string
  addToCart: string
  viewDetails: string
  price: string
  
  // Categories
  categoriesTitle: string
  categoriesDescription: string
  officeWear: string
  industrialWear: string
  healthcareUniforms: string
  
  // Footer
  followUs: string
  allRightsReserved: string
  
  // Admin
  dashboard: string
  products: string
  orders: string
  customers: string
  settings: string
  addProduct: string
  
  // Common
  loading: string
  error: string
  success: string
  save: string
  cancel: string
  delete: string
  edit: string
}

export const translations: Record<Language, Translations> = {
  en: {
    // Navigation
    home: 'Home',
    shop: 'Shop',
    categories: 'Categories',
    about: 'About',
    contact: 'Contact',
    
    // Hero Section
    heroTitle: 'Elite Professional Workwear',
    heroDescription: 'Premium workwear for professionals',
    shopNow: 'Shop Now',
    learnMore: 'Learn More',
    
    // Products
    featuredProducts: 'Elite Collection',
    featuredDescription: 'Handcrafted premium workwear for the most discerning professionals',
    addToCart: 'Add to Cart',
    viewDetails: 'View Details',
    price: 'Price',
    
    // Categories
    categoriesTitle: 'Premium Categories',
    categoriesDescription: 'Explore our exclusive collections designed for excellence',
    officeWear: 'Executive Office Wear',
    industrialWear: 'Premium Industrial Gear',
    healthcareUniforms: 'Luxury Medical Uniforms',
    
    // Footer
    followUs: 'Follow Us',
    allRightsReserved: 'All rights reserved.',
    
    // Admin
    dashboard: 'Dashboard',
    products: 'Products',
    orders: 'Orders',
    customers: 'Customers',
    settings: 'Settings',
    addProduct: 'Add Product',
    
    // Common
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
  },
  
  tr: {
    // Navigation
    home: 'Ana Sayfa',
    shop: 'Mağaza',
    categories: 'Kategoriler',
    about: 'Hakkımızda',
    contact: 'İletişim',
    
    // Hero Section
    heroTitle: 'Elite Profesyonel İş Kıyafetleri',
    heroDescription: 'Profesyoneller için premium iş kıyafetleri',
    shopNow: 'Alışveriş Yap',
    learnMore: 'Daha Fazla',
    
    // Products
    featuredProducts: 'Elite Koleksiyon',
    featuredDescription: 'En seçkin profesyoneller için el yapımı premium iş kıyafetleri',
    addToCart: 'Sepete Ekle',
    viewDetails: 'Detayları Gör',
    price: 'Fiyat',
    
    // Categories
    categoriesTitle: 'Premium Kategoriler',
    categoriesDescription: 'Mükemmellik için tasarlanmış özel koleksiyonlarımızı keşfedin',
    officeWear: 'Yönetici Ofis Kıyafetleri',
    industrialWear: 'Premium Endüstriyel Ekipman',
    healthcareUniforms: 'Lüks Tıbbi Üniformalar',
    
    // Footer
    followUs: 'Bizi Takip Edin',
    allRightsReserved: 'Tüm hakları saklıdır.',
    
    // Admin
    dashboard: 'Kontrol Paneli',
    products: 'Ürünler',
    orders: 'Siparişler',
    customers: 'Müşteriler',
    settings: 'Ayarlar',
    addProduct: 'Ürün Ekle',
    
    // Common
    loading: 'Yükleniyor...',
    error: 'Hata',
    success: 'Başarılı',
    save: 'Kaydet',
    cancel: 'İptal',
    delete: 'Sil',
    edit: 'Düzenle',
  },
  
  de: {
    // Navigation
    home: 'Startseite',
    shop: 'Shop',
    categories: 'Kategorien',
    about: 'Über uns',
    contact: 'Kontakt',
    
    // Hero Section
    heroTitle: 'Elite Professionelle Arbeitskleidung',
    heroDescription: 'Premium Arbeitskleidung für Profis',
    shopNow: 'Jetzt Einkaufen',
    learnMore: 'Mehr Erfahren',
    
    // Products
    featuredProducts: 'Elite Kollektion',
    featuredDescription: 'Handgefertigte Premium-Arbeitskleidung für anspruchsvollste Profis',
    addToCart: 'In den Warenkorb',
    viewDetails: 'Details Anzeigen',
    price: 'Preis',
    
    // Categories
    categoriesTitle: 'Premium Kategorien',
    categoriesDescription: 'Entdecken Sie unsere exklusiven Kollektionen für Exzellenz',
    officeWear: 'Executive Bürokleidung',
    industrialWear: 'Premium Industrieausrüstung',
    healthcareUniforms: 'Luxus Medizinische Uniformen',
    
    // Footer
    followUs: 'Folgen Sie uns',
    allRightsReserved: 'Alle Rechte vorbehalten.',
    
    // Admin
    dashboard: 'Dashboard',
    products: 'Produkte',
    orders: 'Bestellungen',
    customers: 'Kunden',
    settings: 'Einstellungen',
    addProduct: 'Produkt Hinzufügen',
    
    // Common
    loading: 'Laden...',
    error: 'Fehler',
    success: 'Erfolg',
    save: 'Speichern',
    cancel: 'Abbrechen',
    delete: 'Löschen',
    edit: 'Bearbeiten',
  },
}

export function getTranslation(language: Language): Translations {
  return translations[language] || translations.en
}
