interface StructuredDataProps {
  type: 'website' | 'product' | 'organization'
  data: any
}

export function StructuredData({ type, data }: StructuredDataProps) {
  const getStructuredData = () => {
    switch (type) {
      case 'website':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "Elite Work",
          "description": "Ultra Premium Professional Workwear & Luxury Uniforms",
          "url": "https://elitework.com",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "https://elitework.com/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
          }
        }
      
      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "Elite Work",
          "description": "Ultra Premium Professional Workwear & Luxury Uniforms",
          "url": "https://elitework.com",
          "logo": "https://elitework.com/logo.png",
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "******-123-4567",
            "contactType": "customer service",
            "availableLanguage": ["English", "Turkish", "German"]
          },
          "sameAs": [
            "https://facebook.com/elitework",
            "https://instagram.com/elitework",
            "https://twitter.com/elitework"
          ]
        }
      
      case 'product':
        return {
          "@context": "https://schema.org",
          "@type": "Product",
          "name": data.name,
          "description": data.description,
          "image": data.image,
          "brand": {
            "@type": "Brand",
            "name": "Elite Work"
          },
          "offers": {
            "@type": "Offer",
            "price": data.price,
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock",
            "seller": {
              "@type": "Organization",
              "name": "Elite Work"
            }
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": data.rating || 5,
            "reviewCount": data.reviews || 100
          }
        }
      
      default:
        return {}
    }
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData())
      }}
    />
  )
}
