import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // Create categories
  const suitsCategory = await prisma.category.create({
    data: {
      name: 'Executive Suits',
      slug: 'suits',
      description: 'Premium tailored suits for the modern executive',
      image: 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    }
  })

  const shirtsCategory = await prisma.category.create({
    data: {
      name: 'Professional Shirts',
      slug: 'shirts',
      description: 'Professional shirts crafted for comfort and style',
      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    }
  })

  const blazersCategory = await prisma.category.create({
    data: {
      name: 'Designer Blazers',
      slug: 'blazers',
      description: 'Sophisticated blazers for every occasion',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    }
  })

  // Create products
  await prisma.product.create({
    data: {
      name: 'Executive Premium Suit',
      slug: 'executive-premium-suit',
      description: 'A masterfully crafted suit that embodies sophistication and elegance. Made from the finest wool blend, this suit features a modern slim fit that flatters every body type while maintaining comfort throughout your busy day.',
      price: 899.99,
      comparePrice: 1199.99,
      images: [
        'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      ],
      featured: true,
      inventory: 25,
      sku: 'EW-SUIT-001',
      materials: ['Premium Wool Blend', 'Silk Lining'],
      colors: ['Navy', 'Charcoal', 'Black'],
      sizes: ['38R', '40R', '42R', '44R', '46R', '38L', '40L', '42L', '44L'],
      tags: ['premium', 'executive', 'formal'],
      seoTitle: 'Executive Premium Suit - Professional Business Attire',
      seoDescription: 'Elevate your professional wardrobe with our Executive Premium Suit. Crafted from finest materials for the modern executive.',
      categoryId: suitsCategory.id
    }
  })

  await prisma.product.create({
    data: {
      name: 'Premium Work Shirt',
      slug: 'premium-work-shirt',
      description: 'Experience unparalleled comfort and style with our premium work shirt. Crafted from high-quality cotton with moisture-wicking technology, this shirt keeps you looking sharp and feeling fresh all day long.',
      price: 149.99,
      comparePrice: 199.99,
      images: [
        'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      ],
      featured: true,
      inventory: 50,
      sku: 'EW-SHIRT-001',
      materials: ['Premium Cotton', 'Moisture-Wicking Technology'],
      colors: ['White', 'Light Blue', 'Light Pink'],
      sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      tags: ['premium', 'cotton', 'professional'],
      seoTitle: 'Premium Work Shirt - Professional Work Shirts',
      seoDescription: 'Discover our premium work shirts made from high-quality cotton with moisture-wicking technology for all-day comfort.',
      categoryId: shirtsCategory.id
    }
  })

  await prisma.product.create({
    data: {
      name: 'Professional Blazer',
      slug: 'professional-blazer',
      description: 'Make a statement with our professional blazer. This versatile piece combines classic tailoring with modern design elements, perfect for both formal meetings and casual business settings.',
      price: 299.99,
      images: [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      ],
      featured: true,
      inventory: 15,
      sku: 'EW-BLAZER-001',
      materials: ['Wool Blend', 'Polyester Lining'],
      colors: ['Navy', 'Black', 'Grey'],
      sizes: ['36R', '38R', '40R', '42R', '44R'],
      tags: ['professional', 'versatile', 'modern'],
      seoTitle: 'Professional Blazer - Modern Business Blazers',
      seoDescription: 'Elevate your professional look with our modern professional blazer. Perfect for meetings and business casual settings.',
      categoryId: blazersCategory.id
    }
  })

  // Create admin user
  await prisma.user.create({
    data: {
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN'
    }
  })

  console.log('Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
