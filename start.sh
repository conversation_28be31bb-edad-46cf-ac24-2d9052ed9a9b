#!/bin/bash

echo ""
echo "========================================"
echo "   WorkPro - Work Clothing Store"
echo "========================================"
echo ""
echo "Starting your store..."
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed!"
    echo ""
    echo "Please install Node.js first:"
    echo "1. Go to https://nodejs.org/"
    echo "2. Download and install the LTS version"
    echo "3. Restart this script"
    echo ""
    read -p "Press any key to continue..."
    exit 1
fi

echo "✅ Node.js is installed"
echo ""

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    echo "This might take a few minutes..."
    echo ""
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        read -p "Press any key to continue..."
        exit 1
    fi
    echo "✅ Dependencies installed successfully!"
    echo ""
fi

echo "🚀 Starting your WorkPro store..."
echo ""
echo "Your store will open at: http://localhost:3000"
echo "Admin panel will be at: http://localhost:3000/admin"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

npm run dev
