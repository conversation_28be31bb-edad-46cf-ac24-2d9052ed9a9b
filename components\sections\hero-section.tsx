'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { ArrowRightIcon, SparklesIcon, StarIcon } from '@heroicons/react/24/outline'
import { useLanguageStore } from '@/lib/store/language-store'

export function HeroSection() {
  const { translations } = useLanguageStore()

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-24">
      {/* Ultra Premium Background with More Animations */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-elite-900 via-luxury-800 to-premium-900 animate-fade-in"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-luxury-900/60 via-transparent to-premium-900/40 animate-slide-down"></div>

        {/* Many More Animated Elements */}
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            rotate: [0, 360],
            opacity: [0.2, 0.8, 0.2]
          }}
          transition={{ duration: 6, repeat: Infinity }}
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-luxury-400/30 to-premium-400/30 rounded-full blur-3xl animate-pulse-fast"
        />
        <motion.div
          animate={{
            scale: [1.2, 0.8, 1.2],
            rotate: [360, 0],
            opacity: [0.3, 0.9, 0.3]
          }}
          transition={{ duration: 8, repeat: Infinity }}
          className="absolute bottom-20 right-20 w-[500px] h-[500px] bg-gradient-to-r from-premium-500/30 to-vibrant-500/30 rounded-full blur-3xl animate-bounce-slow"
        />

        {/* Many Floating Elements */}
        <motion.div
          animate={{ y: [-30, 30, -30], x: [-15, 15, -15], rotate: [0, 360] }}
          transition={{ duration: 4, repeat: Infinity }}
          className="absolute top-40 right-40 w-6 h-6 bg-luxury-400 rounded-full shadow-lg animate-wiggle"
        />
        <motion.div
          animate={{ y: [25, -25, 25], x: [12, -12, 12], rotate: [360, 0] }}
          transition={{ duration: 5, repeat: Infinity }}
          className="absolute bottom-40 left-40 w-8 h-8 bg-premium-400 rounded-full shadow-lg animate-heart-beat"
        />
        <motion.div
          animate={{ y: [-40, 40, -40], x: [20, -20, 20] }}
          transition={{ duration: 7, repeat: Infinity }}
          className="absolute top-60 left-60 w-4 h-4 bg-vibrant-400 rounded-full shadow-lg animate-flash"
        />
        <motion.div
          animate={{ y: [35, -35, 35], x: [-18, 18, -18] }}
          transition={{ duration: 6, repeat: Infinity }}
          className="absolute bottom-60 right-60 w-5 h-5 bg-luxury-500 rounded-full shadow-lg animate-rotate-slow"
        />

        {/* Additional Floating Shapes */}
        <div className="absolute top-32 left-1/4 w-3 h-3 bg-premium-300 rounded-full animate-bounce-slow"></div>
        <div className="absolute bottom-32 right-1/4 w-4 h-4 bg-vibrant-300 rounded-full animate-pulse-fast"></div>
        <div className="absolute top-1/2 left-20 w-2 h-2 bg-luxury-300 rounded-full animate-wiggle"></div>
        <div className="absolute top-1/2 right-20 w-3 h-3 bg-premium-400 rounded-full animate-heart-beat"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center lg:text-left">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-8"
          >
            <div className="space-y-4">
              <motion.div
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="flex items-center justify-center lg:justify-start space-x-4 mb-6"
              >
                <SparklesIcon className="w-10 h-10 text-luxury-400 animate-wiggle" />
                <span className="text-luxury-300 font-bold text-2xl tracking-wider uppercase font-display animate-glow">
                  Premium Work
                </span>
                <SparklesIcon className="w-10 h-10 text-premium-400 animate-wiggle" />
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.4 }}
                className="text-6xl md:text-8xl lg:text-9xl font-black text-white mb-8 animate-glow text-center lg:text-left"
              >
                {"ELITE WORK".split('').map((letter, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, y: 100, rotateX: -90 }}
                    animate={{ opacity: 1, y: 0, rotateX: 0 }}
                    transition={{
                      duration: 0.8,
                      delay: 0.6 + index * 0.1,
                      type: "spring",
                      stiffness: 100
                    }}
                    className="inline-block hover:animate-tada cursor-default"
                    whileHover={{ scale: 1.2, color: "#eab308" }}
                  >
                    {letter === ' ' ? '\u00A0' : letter}
                  </motion.span>
                ))}
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.2 }}
                className="text-xl text-white/90 leading-relaxed max-w-2xl text-center lg:text-left font-medium animate-fade-in-up"
              >
                Premium workwear for professionals
              </motion.p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.4 }}
              className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start"
            >
              <Link
                href="/shop"
                className="group btn-luxury text-xl px-12 py-5 relative overflow-hidden animate-bounce-slow hover:animate-rubber-band"
              >
                <span className="relative z-10 flex items-center">
                  <SparklesIcon className="w-6 h-6 mr-3 group-hover:animate-wiggle" />
                  Shop Now
                  <ArrowRightIcon className="ml-3 w-6 h-6 group-hover:animate-slide-right" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-premium-500/20 to-vibrant-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-shimmer"></div>
              </Link>

              <Link
                href="/about"
                className="group inline-flex items-center justify-center px-10 py-5 bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-xl border-2 border-luxury-400/50 text-white font-bold text-xl rounded-2xl hover:from-white/30 hover:to-white/20 hover:border-luxury-300 transition-all duration-500 transform hover:scale-105 animate-pulse-fast hover:animate-swing"
              >
                <StarIcon className="w-6 h-6 mr-3 group-hover:animate-rotate-slow" />
                Learn More
              </Link>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="grid grid-cols-3 gap-8 pt-8 border-t border-white/20"
            >
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-white">10K+</div>
                <div className="text-gray-300 text-sm">Happy Customers</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-white">500+</div>
                <div className="text-gray-300 text-sm">Premium Products</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-white">15+</div>
                <div className="text-gray-300 text-sm">Years Experience</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right side - Additional visual element */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="hidden lg:block"
          >
            <div className="relative">
              <div className="absolute inset-0 luxury-gradient rounded-3xl blur-3xl opacity-30 animate-pulse"></div>
              <div className="relative bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20">
                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">✓</span>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">Premium Quality</h3>
                      <p className="text-gray-300 text-sm">Finest materials and craftsmanship</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">✓</span>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">Perfect Fit</h3>
                      <p className="text-gray-300 text-sm">Tailored for professional excellence</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">✓</span>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">Fast Delivery</h3>
                      <p className="text-gray-300 text-sm">Express shipping worldwide</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-white/70 rounded-full mt-2"
          />
        </div>
      </motion.div>
    </section>
  )
}
