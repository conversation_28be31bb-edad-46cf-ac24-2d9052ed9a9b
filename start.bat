@echo off
echo.
echo ========================================
echo    WorkPro - Work Clothing Store
echo ========================================
echo.
echo Starting your store...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo.
    echo Please install Node.js first:
    echo 1. Go to https://nodejs.org/
    echo 2. Download and install the LTS version
    echo 3. Restart this script
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js is installed
echo.

REM Check if dependencies are installed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    echo This might take a few minutes...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully!
    echo.
)

echo 🚀 Starting your WorkPro store...
echo.
echo Your store will open at: http://localhost:3000
echo Admin panel will be at: http://localhost:3000/admin
echo.
echo Press Ctrl+C to stop the server
echo.

npm run dev
