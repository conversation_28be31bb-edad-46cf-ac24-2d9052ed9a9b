import { Metada<PERSON> } from 'next'
import { <PERSON><PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'

export const metadata: Metadata = {
  title: 'Contact Us | WorkPro',
  description: 'Get in touch with WorkPro for questions about work clothing and orders.',
}

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-display font-bold text-gray-900 mb-4">
              Contact Us
            </h1>
            <p className="text-xl text-gray-600">
              We're here to help! Get in touch with any questions.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-blue-50 rounded-2xl p-8">
              <h2 className="text-2xl font-bold text-blue-900 mb-6">Send us a message</h2>
              <form className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter your name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter your email"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subject
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="What's this about?"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message
                  </label>
                  <textarea
                    rows={5}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Tell us how we can help..."
                  />
                </div>
                
                <button type="submit" className="w-full btn-modern py-3">
                  Send Message
                </button>
              </form>
            </div>

            {/* Contact Info */}
            <div className="space-y-8">
              <div className="bg-green-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-green-900 mb-4">📧 Email Us</h3>
                <p className="text-green-800 mb-2">
                  <strong>General Questions:</strong><br />
                  <EMAIL>
                </p>
                <p className="text-green-800">
                  <strong>Order Support:</strong><br />
                  <EMAIL>
                </p>
              </div>

              <div className="bg-purple-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-purple-900 mb-4">📞 Call Us</h3>
                <p className="text-purple-800 mb-2">
                  <strong>Customer Service:</strong><br />
                  (555) 123-4567
                </p>
                <p className="text-purple-800 text-sm">
                  Monday - Friday: 9 AM - 6 PM EST<br />
                  Saturday: 10 AM - 4 PM EST
                </p>
              </div>

              <div className="bg-orange-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-orange-900 mb-4">🏢 Visit Us</h3>
                <p className="text-orange-800">
                  WorkPro Headquarters<br />
                  123 Business Street<br />
                  Suite 100<br />
                  Your City, ST 12345
                </p>
              </div>

              <div className="bg-indigo-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-indigo-900 mb-4">❓ FAQ</h3>
                <p className="text-indigo-800 mb-3">
                  Check our frequently asked questions for quick answers to common questions.
                </p>
                <button className="text-indigo-600 font-medium hover:text-indigo-800">
                  View FAQ →
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
