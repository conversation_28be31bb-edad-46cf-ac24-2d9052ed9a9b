'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSession, signIn, signOut } from 'next-auth/react'
import { ShoppingBagIcon, UserIcon, MagnifyingGlassIcon, Bars3Icon, XMarkIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { motion, AnimatePresence } from 'framer-motion'
import { useCartStore } from '@/lib/store/cart-store'
import { useLanguageStore } from '@/lib/store/language-store'
import { SearchModal } from '@/components/modals/search-modal'
import { CartSidebar } from '@/components/cart/cart-sidebar'
import { LanguageSelector } from '@/components/ui/language-selector'

// Navigation will be dynamic based on language

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [isCartOpen, setIsCartOpen] = useState(false)
  const { data: session } = useSession()
  const { items } = useCartStore()
  const { translations } = useLanguageStore()

  const itemCount = items.reduce((total, item) => total + item.quantity, 0)

  const navigation = [
    { name: translations.home, href: '/' },
    { name: translations.shop, href: '/shop' },
    { name: translations.categories, href: '/categories' },
    { name: translations.about, href: '/about' },
    { name: translations.contact, href: '/contact' },
  ]

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <>
      {/* Main Header with More Animations */}
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 transform ${
        isScrolled
          ? 'bg-gradient-to-r from-white/95 via-luxury-50/90 to-white/95 backdrop-blur-xl shadow-2xl border-b border-luxury-200/50 animate-slide-down'
          : 'bg-gradient-to-r from-transparent via-white/10 to-transparent backdrop-blur-sm animate-fade-in'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20 lg:h-24">
            {/* Logo with More Animations */}
            <Link href="/" className="flex items-center space-x-4 group animate-fade-in-left">
              <div className="relative">
                <div className="w-12 h-12 lg:w-16 lg:h-16 luxury-gradient rounded-2xl flex items-center justify-center shadow-2xl group-hover:shadow-luxury-500/50 transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-12 border-2 border-luxury-400/50 animate-bounce-slow">
                  <SparklesIcon className="w-6 h-6 lg:w-8 lg:h-8 text-white animate-pulse group-hover:animate-wiggle" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-premium-500 rounded-full animate-heart-beat"></div>
                <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-vibrant-500 rounded-full animate-flash"></div>
              </div>
              <div className="flex flex-col">
                <span className="font-display font-black text-2xl lg:text-3xl luxury-text-gradient animate-glow group-hover:animate-tada">
                  ELITE WORK
                </span>
                <span className="text-xs lg:text-sm text-elite-600 font-bold tracking-wider uppercase animate-slide-right">
                  Premium Collection
                </span>
              </div>
            </Link>

            {/* Desktop Navigation with More Animations */}
            <nav className="hidden lg:flex items-center space-x-10 animate-fade-in-down">
              {navigation.map((item, index) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`relative text-elite-800 hover:text-luxury-600 font-bold text-lg transition-all duration-500 group animate-fade-in-up`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <span className="relative z-10 group-hover:animate-rubber-band">{item.name}</span>
                  <span className="absolute -bottom-2 left-0 w-0 h-1 bg-gradient-to-r from-luxury-500 to-premium-500 transition-all duration-500 group-hover:w-full rounded-full group-hover:animate-shimmer"></span>
                  <span className="absolute inset-0 bg-gradient-to-r from-luxury-100/0 to-premium-100/0 group-hover:from-luxury-100/50 group-hover:to-premium-100/50 rounded-lg transition-all duration-300 -z-10 group-hover:animate-pulse-fast"></span>
                </Link>
              ))}
            </nav>

            {/* Actions */}
            <div className="flex items-center space-x-6">
              {/* Language Selector */}
              <LanguageSelector />

              {/* Search */}
              <button
                onClick={() => setIsSearchOpen(true)}
                className="p-3 text-elite-700 hover:text-luxury-600 transition-all duration-300 bg-luxury-100/50 hover:bg-luxury-200/50 rounded-xl backdrop-blur-sm border border-luxury-200/50 hover:border-luxury-400/50"
                aria-label="Search"
              >
                <MagnifyingGlassIcon className="w-6 h-6" />
              </button>

              {/* User Account */}
              <div className="relative group">
                <button className="p-2 text-gray-700 hover:text-blue-600 transition-colors duration-200">
                  <UserIcon className="w-6 h-6" />
                </button>
                <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  {session ? (
                    <div className="py-2">
                      <div className="px-4 py-2 text-sm text-gray-700 border-b">
                        {session.user?.name || session.user?.email}
                      </div>
                      <Link href="/account" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        My Account
                      </Link>
                      <Link href="/orders" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        Orders
                      </Link>
                      <button
                        onClick={() => signOut()}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        Sign Out
                      </button>
                    </div>
                  ) : (
                    <div className="py-2">
                      <button
                        onClick={() => signIn()}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        Sign In
                      </button>
                      <Link href="/register" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        Create Account
                      </Link>
                    </div>
                  )}
                </div>
              </div>

              {/* Shopping Cart */}
              <button
                onClick={() => setIsCartOpen(true)}
                className="relative p-3 text-elite-700 hover:text-luxury-600 transition-all duration-300 bg-luxury-100/50 hover:bg-luxury-200/50 rounded-xl backdrop-blur-sm border border-luxury-200/50 hover:border-luxury-400/50 group"
                aria-label="Shopping cart"
              >
                <ShoppingBagIcon className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                {itemCount > 0 && (
                  <span className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-premium-500 to-vibrant-500 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg animate-pulse">
                    {itemCount}
                  </span>
                )}
              </button>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                aria-label="Toggle menu"
              >
                {isMobileMenuOpen ? (
                  <XMarkIcon className="w-6 h-6" />
                ) : (
                  <Bars3Icon className="w-6 h-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden bg-white border-t"
            >
              <div className="px-4 py-4 space-y-4">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </header>

      {/* Search Modal */}
      <SearchModal isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />

      {/* Cart Sidebar */}
      <CartSidebar isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />
    </>
  )
}
