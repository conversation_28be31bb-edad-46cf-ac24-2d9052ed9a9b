'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSession, signIn, signOut } from 'next-auth/react'
import { ShoppingBagIcon, UserIcon, MagnifyingGlassIcon, Bars3Icon, XMarkIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { motion, AnimatePresence } from 'framer-motion'
import { useCartStore } from '@/lib/store/cart-store'
import { useLanguageStore } from '@/lib/store/language-store'
import { SearchModal } from '@/components/modals/search-modal'
import { CartSidebar } from '@/components/cart/cart-sidebar'
import { LanguageSelector } from '@/components/ui/language-selector'

// Navigation will be dynamic based on language

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [isCartOpen, setIsCartOpen] = useState(false)
  const { data: session } = useSession()
  const { items } = useCartStore()
  const { translations } = useLanguageStore()

  const itemCount = items.reduce((total, item) => total + item.quantity, 0)

  const navigation = [
    { name: translations.home, href: '/' },
    { name: translations.shop, href: '/shop' },
    { name: translations.categories, href: '/categories' },
    { name: translations.about, href: '/about' },
    { name: translations.contact, href: '/contact' },
  ]

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <>
      {/* Classic Professional Header */}
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white shadow-lg border-b border-gray-200'
          : 'bg-white/95 backdrop-blur-sm'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20 lg:h-24">
            {/* Classic Professional Logo */}
            <Link href="/" className="flex items-center space-x-3 group animate-classic-fade">
              <div className="relative">
                <div className="w-10 h-10 lg:w-12 lg:h-12 classic-gradient rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-105">
                  <SparklesIcon className="w-5 h-5 lg:w-6 lg:h-6 text-white" />
                </div>
              </div>
              <div className="flex flex-col">
                <span className="font-display font-bold text-xl lg:text-2xl text-classic-900">
                  ELITE WORK
                </span>
                <span className="text-xs lg:text-sm text-classic-600 font-medium tracking-wide">
                  Professional Clothing
                </span>
              </div>
            </Link>

            {/* Classic Professional Navigation */}
            <nav className="hidden lg:flex items-center space-x-8 animate-classic-slide">
              {navigation.map((item, index) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="relative text-classic-700 hover:text-classic-900 font-semibold text-base transition-all duration-300 group animate-classic-hover"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <span className="relative z-10">{item.name}</span>
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-classic-600 transition-all duration-300 group-hover:w-full animate-underline"></span>
                </Link>
              ))}
            </nav>

            {/* Actions */}
            <div className="flex items-center space-x-6">
              {/* Language Selector */}
              <LanguageSelector />

              {/* Search */}
              <button
                onClick={() => setIsSearchOpen(true)}
                className="p-2 text-classic-600 hover:text-classic-800 transition-all duration-300 bg-gray-100 hover:bg-gray-200 rounded-lg animate-classic-hover"
                aria-label="Search"
              >
                <MagnifyingGlassIcon className="w-5 h-5" />
              </button>

              {/* User Account */}
              <div className="relative group">
                <button className="p-2 text-gray-700 hover:text-blue-600 transition-colors duration-200">
                  <UserIcon className="w-6 h-6" />
                </button>
                <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  {session ? (
                    <div className="py-2">
                      <div className="px-4 py-2 text-sm text-gray-700 border-b">
                        {session.user?.name || session.user?.email}
                      </div>
                      <Link href="/account" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        My Account
                      </Link>
                      <Link href="/orders" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        Orders
                      </Link>
                      <button
                        onClick={() => signOut()}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        Sign Out
                      </button>
                    </div>
                  ) : (
                    <div className="py-2">
                      <button
                        onClick={() => signIn()}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        Sign In
                      </button>
                      <Link href="/register" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        Create Account
                      </Link>
                    </div>
                  )}
                </div>
              </div>

              {/* Shopping Cart */}
              <button
                onClick={() => setIsCartOpen(true)}
                className="relative p-2 text-classic-600 hover:text-classic-800 transition-all duration-300 bg-gray-100 hover:bg-gray-200 rounded-lg animate-classic-hover group"
                aria-label="Shopping cart"
              >
                <ShoppingBagIcon className="w-5 h-5 group-hover:scale-105 transition-transform duration-300" />
                {itemCount > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-classic-600 text-white text-xs rounded-full flex items-center justify-center font-semibold shadow-md animate-elegant-pulse">
                    {itemCount}
                  </span>
                )}
              </button>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                aria-label="Toggle menu"
              >
                {isMobileMenuOpen ? (
                  <XMarkIcon className="w-6 h-6" />
                ) : (
                  <Bars3Icon className="w-6 h-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden bg-white border-t"
            >
              <div className="px-4 py-4 space-y-4">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </header>

      {/* Search Modal */}
      <SearchModal isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />

      {/* Cart Sidebar */}
      <CartSidebar isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />
    </>
  )
}
